"""
Chatbot logic using OpenAI API for the Galaxy Mentor Assistant.
"""

import openai
import os
from typing import List, Dict

class GalaxyMentor:
    """
    A personal mentor chatbot that provides guidance on self-development,
    productivity, programming, and tech tips with a galaxy-themed personality.
    """
    
    def __init__(self):
        """Initialize the Galaxy Mentor with OpenAI API."""
        openai.api_key = os.getenv('OPENAI_API_KEY')
        
        # System prompt that defines the mentor's personality and expertise
        self.system_prompt = """
        You are Galaxy Mentor, a wise and supportive AI assistant with a cosmic perspective on personal growth and technology. 
        
        Your personality:
        - Wise and encouraging, like a mentor who has seen the vastness of the universe
        - Use occasional space/galaxy metaphors naturally in your responses
        - Supportive but honest, helping users navigate their journey of growth
        - Knowledgeable about self-development, productivity, programming, and technology
        
        Your expertise areas:
        - Self-development and personal growth
        - Productivity techniques and time management
        - Programming languages, best practices, and learning paths
        - Technology trends and career advice in tech
        - Learning strategies and skill development
        
        Guidelines:
        - Keep responses helpful, actionable, and encouraging
        - Provide specific, practical advice when possible
        - Ask clarifying questions when needed
        - Use a warm, mentoring tone
        - Occasionally use space/cosmic metaphors but don't overdo it
        - Keep responses concise but comprehensive (aim for 2-4 paragraphs)
        """
    
    def get_response(self, user_message: str, conversation_history: List[Dict] = None) -> str:
        """
        Get a response from the Galaxy Mentor based on user input.
        
        Args:
            user_message (str): The user's message
            conversation_history (List[Dict]): Previous conversation context
            
        Returns:
            str: The mentor's response
        """
        try:
            # Build the conversation context
            messages = [{"role": "system", "content": self.system_prompt}]
            
            # Add conversation history if provided (last 5 exchanges to manage token usage)
            if conversation_history:
                for exchange in conversation_history[-5:]:
                    messages.append({"role": "user", "content": exchange.get("user", "")})
                    messages.append({"role": "assistant", "content": exchange.get("assistant", "")})
            
            # Add the current user message
            messages.append({"role": "user", "content": user_message})
            
            # Get response from OpenAI
            from openai import OpenAI
            client = OpenAI(api_key=openai.api_key)

            response = client.chat.completions.create(
                model="gpt-3.5-turbo",
                messages=messages,
                max_tokens=500,
                temperature=0.7,
                presence_penalty=0.1,
                frequency_penalty=0.1
            )

            return response.choices[0].message.content.strip()
            
        except Exception as e:
            error_message = str(e).lower()
            if "authentication" in error_message or "api key" in error_message:
                return "🌌 I'm having trouble connecting to my cosmic knowledge base. Please check if the OpenAI API key is properly configured."
            elif "rate limit" in error_message or "quota" in error_message:
                return "🌟 The cosmic channels are a bit busy right now. Please try again in a moment."
            elif "api" in error_message:
                return "🚀 I encountered a technical issue while processing your request. Please try again later."
            else:
                return "🌌 Something unexpected happened in the cosmic realm. Please try your question again."
    
    def get_welcome_message(self, username: str) -> str:
        """
        Generate a personalized welcome message for new users.
        
        Args:
            username (str): The user's name
            
        Returns:
            str: Welcome message
        """
        return f"""🌌 Welcome to the cosmic realm, {username}! I'm Galaxy Mentor, your personal guide through the vast universe of self-development, productivity, and technology.

Whether you're looking to:
⭐ Develop new skills and grow personally
🚀 Boost your productivity and manage time better  
💻 Learn programming or advance your tech career
🌟 Get practical advice on technology and tools

I'm here to help you navigate your journey with wisdom from across the digital cosmos. What would you like to explore today?"""
