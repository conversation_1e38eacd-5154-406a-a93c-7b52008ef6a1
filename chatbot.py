# chatbot.py

import os
import requests
from dotenv import load_dotenv

load_dotenv()

GOOGLE_API_KEY = os.getenv("GOOGLE_API_KEY")

class GalaxyMentor:
    def __init__(self):
        self.api_url = f"https://generativelanguage.googleapis.com/v1beta/models/gemini-pro:generateContent?key={GOOGLE_API_KEY}"

    def get_response(self, user_message, history=[]):
        """Generate a response using Gemini API based on user message and conversation history."""

        # Optionally include history (not mandatory for Gemini)
        content_parts = []
        for pair in history:
            content_parts.append({"text": f"You: {pair['user']}"})
            content_parts.append({"text": f"Mentor: {pair['assistant']}"})

        content_parts.append({"text": user_message})

        payload = {
            "contents": [
                {
                    "parts": content_parts
                }
            ]
        }

        try:
            response = requests.post(self.api_url, json=payload)
            result = response.json()
            return result['candidates'][0]['content']['parts'][0]['text']
        except Exception as e:
            print("Gemini API error:", e)
            return "🌌 The cosmic channels are a bit busy right now. Please try again in a moment."

    def get_welcome_message(self, username):
        return f"🌠 Welcome back, {username}! How can I guide you today on your journey?"
