<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Chatbot Backend Test</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 20px;
        }

        .chat-container {
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            width: 100%;
            max-width: 600px;
            height: 600px;
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }

        .chat-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            text-align: center;
        }

        .chat-header h1 {
            margin: 0;
            font-size: 1.5rem;
        }

        .status {
            margin-top: 10px;
            padding: 5px 10px;
            border-radius: 15px;
            background: rgba(255,255,255,0.2);
            font-size: 0.9rem;
        }

        .chat-messages {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
            display: flex;
            flex-direction: column;
            gap: 15px;
        }

        .message {
            max-width: 80%;
            padding: 12px 16px;
            border-radius: 18px;
            word-wrap: break-word;
        }

        .user-message {
            align-self: flex-end;
            background: #667eea;
            color: white;
        }

        .bot-message {
            align-self: flex-start;
            background: #f1f3f4;
            color: #333;
        }

        .error-message {
            align-self: center;
            background: #ff6b6b;
            color: white;
            text-align: center;
        }

        .chat-input {
            padding: 20px;
            border-top: 1px solid #eee;
            display: flex;
            gap: 10px;
        }

        .chat-input input {
            flex: 1;
            padding: 12px 16px;
            border: 2px solid #eee;
            border-radius: 25px;
            outline: none;
            font-size: 1rem;
        }

        .chat-input input:focus {
            border-color: #667eea;
        }

        .chat-input button {
            padding: 12px 20px;
            background: #667eea;
            color: white;
            border: none;
            border-radius: 25px;
            cursor: pointer;
            font-size: 1rem;
            transition: background 0.3s;
        }

        .chat-input button:hover {
            background: #5a6fd8;
        }

        .chat-input button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }

        .typing {
            align-self: flex-start;
            background: #f1f3f4;
            color: #666;
            font-style: italic;
            animation: pulse 1.5s infinite;
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }

        .metadata {
            font-size: 0.8rem;
            color: #666;
            margin-top: 5px;
        }
    </style>
</head>
<body>
    <div class="chat-container">
        <div class="chat-header">
            <h1>🤖 Chatbot Backend Test</h1>
            <div class="status" id="status">Checking connection...</div>
        </div>
        
        <div class="chat-messages" id="messages">
            <div class="message bot-message">
                👋 Hello! I'm your AI assistant. How can I help you today?
            </div>
        </div>
        
        <div class="chat-input">
            <input type="text" id="messageInput" placeholder="Type your message..." maxlength="1000">
            <button id="sendButton" onclick="sendMessage()">Send</button>
        </div>
    </div>

    <script>
        const API_BASE_URL = 'http://localhost:5000';
        let conversationHistory = [];
        let isLoading = false;

        // DOM elements
        const messagesContainer = document.getElementById('messages');
        const messageInput = document.getElementById('messageInput');
        const sendButton = document.getElementById('sendButton');
        const statusElement = document.getElementById('status');

        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            checkBackendHealth();
            messageInput.addEventListener('keypress', function(e) {
                if (e.key === 'Enter' && !e.shiftKey) {
                    e.preventDefault();
                    sendMessage();
                }
            });
        });

        async function checkBackendHealth() {
            try {
                const response = await fetch(`${API_BASE_URL}/`);
                const data = await response.json();
                
                if (data.status === 'healthy') {
                    statusElement.textContent = '✅ Backend Connected';
                    statusElement.style.background = 'rgba(76, 175, 80, 0.3)';
                } else {
                    throw new Error('Backend not healthy');
                }
            } catch (error) {
                statusElement.textContent = '❌ Backend Disconnected';
                statusElement.style.background = 'rgba(244, 67, 54, 0.3)';
                addMessage('error', 'Cannot connect to backend. Please ensure the server is running on http://localhost:5000');
            }
        }

        async function sendMessage() {
            const message = messageInput.value.trim();
            
            if (!message || isLoading) return;

            // Add user message to chat
            addMessage('user', message);
            messageInput.value = '';
            
            // Show typing indicator
            const typingId = addMessage('typing', 'Thinking...');
            
            // Disable input
            setLoading(true);

            try {
                const response = await fetch(`${API_BASE_URL}/api/chat`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        message: message,
                        conversation_history: conversationHistory
                    })
                });

                const data = await response.json();
                
                // Remove typing indicator
                removeMessage(typingId);

                if (data.success) {
                    // Add bot response
                    addMessage('bot', data.message, {
                        model: data.model,
                        tokens: data.tokens_used,
                        timestamp: data.timestamp
                    });
                    
                    // Update conversation history
                    conversationHistory.push({
                        user: message,
                        assistant: data.message
                    });
                } else {
                    addMessage('error', `Error: ${data.error}`);
                }

            } catch (error) {
                removeMessage(typingId);
                addMessage('error', `Network error: ${error.message}`);
            } finally {
                setLoading(false);
            }
        }

        function addMessage(type, content, metadata = null) {
            const messageDiv = document.createElement('div');
            const messageId = 'msg-' + Date.now() + '-' + Math.random().toString(36).substr(2, 9);
            messageDiv.id = messageId;
            
            if (type === 'user') {
                messageDiv.className = 'message user-message';
                messageDiv.textContent = content;
            } else if (type === 'bot') {
                messageDiv.className = 'message bot-message';
                messageDiv.textContent = content;
                
                if (metadata) {
                    const metaDiv = document.createElement('div');
                    metaDiv.className = 'metadata';
                    metaDiv.textContent = `Model: ${metadata.model} | Tokens: ${metadata.tokens || 'N/A'}`;
                    messageDiv.appendChild(metaDiv);
                }
            } else if (type === 'error') {
                messageDiv.className = 'message error-message';
                messageDiv.textContent = content;
            } else if (type === 'typing') {
                messageDiv.className = 'message typing';
                messageDiv.textContent = content;
            }

            messagesContainer.appendChild(messageDiv);
            messagesContainer.scrollTop = messagesContainer.scrollHeight;
            
            return messageId;
        }

        function removeMessage(messageId) {
            const messageElement = document.getElementById(messageId);
            if (messageElement) {
                messageElement.remove();
            }
        }

        function setLoading(loading) {
            isLoading = loading;
            sendButton.disabled = loading;
            messageInput.disabled = loading;
            
            if (loading) {
                sendButton.textContent = 'Sending...';
            } else {
                sendButton.textContent = 'Send';
                messageInput.focus();
            }
        }

        // Clear conversation history
        function clearHistory() {
            conversationHistory = [];
            messagesContainer.innerHTML = '<div class="message bot-message">👋 Hello! I\'m your AI assistant. How can I help you today?</div>';
        }

        // Add clear button functionality (you can add this to the UI if needed)
        document.addEventListener('keydown', function(e) {
            if (e.ctrlKey && e.key === 'l') {
                e.preventDefault();
                clearHistory();
            }
        });
    </script>
</body>
</html>
