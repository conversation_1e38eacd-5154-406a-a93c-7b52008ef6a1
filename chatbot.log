2025-06-02 00:59:19,582 - __main__ - ERROR - Failed to initialize OpenAI client: Client.__init__() got an unexpected keyword argument 'proxies'
2025-06-02 01:00:12,550 - __main__ - INFO - OpenAI client initialized successfully
2025-06-02 01:00:12,552 - __main__ - INFO - Starting Chatbot Backend Server...
2025-06-02 01:00:12,552 - __main__ - INFO - OpenAI API Key configured: Yes
2025-06-02 01:00:12,573 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://************:5000
2025-06-02 01:00:12,573 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-06-02 01:00:12,576 - werkzeug - INFO -  * Restarting with stat
2025-06-02 01:00:13,947 - __main__ - INFO - OpenAI client initialized successfully
2025-06-02 01:00:13,949 - __main__ - INFO - Starting Chatbot Backend Server...
2025-06-02 01:00:13,950 - __main__ - INFO - OpenAI API Key configured: Yes
2025-06-02 01:00:13,958 - werkzeug - WARNING -  * Debugger is active!
2025-06-02 01:00:13,963 - werkzeug - INFO -  * Debugger PIN: 111-055-101
2025-06-02 01:03:32,352 - __main__ - INFO - OpenAI client initialized successfully
2025-06-02 01:03:32,353 - __main__ - INFO - Starting Chatbot Backend Server...
2025-06-02 01:03:32,354 - __main__ - INFO - OpenAI API Key configured: Yes
2025-06-02 01:03:32,376 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://************:5000
2025-06-02 01:03:32,376 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-06-02 01:03:32,378 - werkzeug - INFO -  * Restarting with stat
2025-06-02 01:03:33,730 - __main__ - INFO - OpenAI client initialized successfully
2025-06-02 01:03:33,731 - __main__ - INFO - Starting Chatbot Backend Server...
2025-06-02 01:03:33,732 - __main__ - INFO - OpenAI API Key configured: Yes
2025-06-02 01:03:33,740 - werkzeug - WARNING -  * Debugger is active!
2025-06-02 01:03:33,746 - werkzeug - INFO -  * Debugger PIN: 111-055-101
2025-06-02 01:05:09,006 - werkzeug - INFO - 127.0.0.1 - - [02/Jun/2025 01:05:09] "GET / HTTP/1.1" 200 -
2025-06-02 01:05:18,130 - __main__ - INFO - Processing chat request. Message length: 25
2025-06-02 01:05:18,130 - __main__ - INFO - Sending request to OpenAI with 2 messages
2025-06-02 01:05:19,895 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-06-02 01:05:19,896 - openai._base_client - INFO - Retrying request to /chat/completions in 0.487151 seconds
2025-06-02 01:05:21,090 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-06-02 01:05:21,091 - openai._base_client - INFO - Retrying request to /chat/completions in 0.985371 seconds
2025-06-02 01:05:22,264 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-06-02 01:05:22,267 - __main__ - ERROR - OpenAI API error: Error code: 429 - {'error': {'message': 'You exceeded your current quota, please check your plan and billing details. For more information on this error, read the docs: https://platform.openai.com/docs/guides/error-codes/api-errors.', 'type': 'insufficient_quota', 'param': None, 'code': 'insufficient_quota'}}
2025-06-02 01:05:22,270 - __main__ - ERROR - Traceback: Traceback (most recent call last):
  File "C:\Users\<USER>\OneDrive\Bureau\projets augmentex\chatbot attempt1\chatbot_backend.py", line 107, in get_response
    response = self.client.chat.completions.create(
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\openai\_utils\_utils.py", line 287, in wrapper
    return func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\openai\resources\chat\completions\completions.py", line 925, in create
    return self._post(
           ^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\openai\_base_client.py", line 1239, in post
    return cast(ResponseT, self.request(cast_to, opts, stream=stream, stream_cls=stream_cls))
                           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\openai\_base_client.py", line 1034, in request
    raise self._make_status_error_from_response(err.response) from None
openai.RateLimitError: Error code: 429 - {'error': {'message': 'You exceeded your current quota, please check your plan and billing details. For more information on this error, read the docs: https://platform.openai.com/docs/guides/error-codes/api-errors.', 'type': 'insufficient_quota', 'param': None, 'code': 'insufficient_quota'}}

2025-06-02 01:05:22,272 - werkzeug - INFO - 127.0.0.1 - - [02/Jun/2025 01:05:22] "[35m[1mPOST /api/chat HTTP/1.1[0m" 500 -
2025-06-02 01:06:12,852 - werkzeug - INFO - 127.0.0.1 - - [02/Jun/2025 01:06:12] "GET / HTTP/1.1" 200 -
2025-06-02 01:06:21,259 - werkzeug - INFO - 127.0.0.1 - - [02/Jun/2025 01:06:21] "GET /api/chat/models HTTP/1.1" 200 -
2025-06-02 01:06:28,426 - werkzeug - INFO - 127.0.0.1 - - [02/Jun/2025 01:06:28] "GET /api/chat/config HTTP/1.1" 200 -
2025-06-02 01:06:35,177 - werkzeug - INFO - 127.0.0.1 - - [02/Jun/2025 01:06:35] "GET / HTTP/1.1" 200 -
2025-06-02 01:06:40,866 - werkzeug - INFO - 127.0.0.1 - - [02/Jun/2025 01:06:40] "OPTIONS /api/chat HTTP/1.1" 200 -
2025-06-02 01:06:41,181 - __main__ - INFO - Processing chat request. Message length: 3
2025-06-02 01:06:41,182 - __main__ - INFO - Sending request to OpenAI with 2 messages
2025-06-02 01:06:41,746 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-06-02 01:06:41,748 - openai._base_client - INFO - Retrying request to /chat/completions in 0.488031 seconds
2025-06-02 01:06:42,916 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-06-02 01:06:42,919 - openai._base_client - INFO - Retrying request to /chat/completions in 0.869808 seconds
2025-06-02 01:06:44,268 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-06-02 01:06:44,271 - __main__ - ERROR - OpenAI API error: Error code: 429 - {'error': {'message': 'You exceeded your current quota, please check your plan and billing details. For more information on this error, read the docs: https://platform.openai.com/docs/guides/error-codes/api-errors.', 'type': 'insufficient_quota', 'param': None, 'code': 'insufficient_quota'}}
2025-06-02 01:06:44,273 - __main__ - ERROR - Traceback: Traceback (most recent call last):
  File "C:\Users\<USER>\OneDrive\Bureau\projets augmentex\chatbot attempt1\chatbot_backend.py", line 107, in get_response
    response = self.client.chat.completions.create(
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\openai\_utils\_utils.py", line 287, in wrapper
    return func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\openai\resources\chat\completions\completions.py", line 925, in create
    return self._post(
           ^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\openai\_base_client.py", line 1239, in post
    return cast(ResponseT, self.request(cast_to, opts, stream=stream, stream_cls=stream_cls))
                           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\openai\_base_client.py", line 1034, in request
    raise self._make_status_error_from_response(err.response) from None
openai.RateLimitError: Error code: 429 - {'error': {'message': 'You exceeded your current quota, please check your plan and billing details. For more information on this error, read the docs: https://platform.openai.com/docs/guides/error-codes/api-errors.', 'type': 'insufficient_quota', 'param': None, 'code': 'insufficient_quota'}}

2025-06-02 01:06:44,276 - werkzeug - INFO - 127.0.0.1 - - [02/Jun/2025 01:06:44] "[35m[1mPOST /api/chat HTTP/1.1[0m" 500 -
2025-06-02 01:07:25,688 - werkzeug - INFO - 127.0.0.1 - - [02/Jun/2025 01:07:25] "GET / HTTP/1.1" 200 -
2025-06-02 01:07:27,738 - werkzeug - INFO - 127.0.0.1 - - [02/Jun/2025 01:07:27] "GET /api/chat/config HTTP/1.1" 200 -
2025-06-02 01:07:44,123 - __main__ - INFO - Processing chat request. Message length: 46
2025-06-02 01:07:44,124 - __main__ - INFO - Sending request to OpenAI with 2 messages
2025-06-02 01:07:45,343 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-06-02 01:07:45,344 - openai._base_client - INFO - Retrying request to /chat/completions in 0.476724 seconds
2025-06-02 01:07:46,664 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-06-02 01:07:46,666 - openai._base_client - INFO - Retrying request to /chat/completions in 0.810272 seconds
2025-06-02 01:07:47,948 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-06-02 01:07:47,950 - __main__ - ERROR - OpenAI API error: Error code: 429 - {'error': {'message': 'You exceeded your current quota, please check your plan and billing details. For more information on this error, read the docs: https://platform.openai.com/docs/guides/error-codes/api-errors.', 'type': 'insufficient_quota', 'param': None, 'code': 'insufficient_quota'}}
2025-06-02 01:07:47,952 - __main__ - ERROR - Traceback: Traceback (most recent call last):
  File "C:\Users\<USER>\OneDrive\Bureau\projets augmentex\chatbot attempt1\chatbot_backend.py", line 107, in get_response
    response = self.client.chat.completions.create(
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\openai\_utils\_utils.py", line 287, in wrapper
    return func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\openai\resources\chat\completions\completions.py", line 925, in create
    return self._post(
           ^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\openai\_base_client.py", line 1239, in post
    return cast(ResponseT, self.request(cast_to, opts, stream=stream, stream_cls=stream_cls))
                           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\openai\_base_client.py", line 1034, in request
    raise self._make_status_error_from_response(err.response) from None
openai.RateLimitError: Error code: 429 - {'error': {'message': 'You exceeded your current quota, please check your plan and billing details. For more information on this error, read the docs: https://platform.openai.com/docs/guides/error-codes/api-errors.', 'type': 'insufficient_quota', 'param': None, 'code': 'insufficient_quota'}}

2025-06-02 01:07:47,954 - werkzeug - INFO - 127.0.0.1 - - [02/Jun/2025 01:07:47] "[35m[1mPOST /api/chat HTTP/1.1[0m" 500 -
2025-06-02 01:08:58,337 - werkzeug - INFO - 127.0.0.1 - - [02/Jun/2025 01:08:58] "GET / HTTP/1.1" 200 -
