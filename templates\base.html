<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}Galaxy Mentor - Your Personal AI Coach{% endblock %}</title>
    
    <!-- CSS -->
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">
    
    <!-- Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Space+Grotesk:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    {% block head %}{% endblock %}
</head>
<body>
    <!-- Animated background -->
    <div class="galaxy-background">
        <div class="stars"></div>
        <div class="stars2"></div>
        <div class="stars3"></div>
    </div>
    
    <!-- Navigation -->
    <nav class="navbar">
        <div class="nav-container">
            <div class="nav-brand">
                <i class="fas fa-rocket"></i>
                <span>Galaxy Mentor</span>
            </div>
            
            <div class="nav-menu">
                {% if session.user_id %}
                    <span class="nav-user">
                        <i class="fas fa-user-astronaut"></i>
                        {{ session.username }}
                    </span>
                    <a href="{{ url_for('logout') }}" class="nav-link">
                        <i class="fas fa-sign-out-alt"></i>
                        Logout
                    </a>
                {% else %}
                    <a href="{{ url_for('login') }}" class="nav-link">
                        <i class="fas fa-sign-in-alt"></i>
                        Login
                    </a>
                    <a href="{{ url_for('register') }}" class="nav-link nav-cta">
                        <i class="fas fa-user-plus"></i>
                        Sign Up
                    </a>
                {% endif %}
            </div>
        </div>
    </nav>
    
    <!-- Flash messages -->
    {% with messages = get_flashed_messages(with_categories=true) %}
        {% if messages %}
            <div class="flash-container">
                {% for category, message in messages %}
                    <div class="flash-message flash-{{ category }}">
                        <i class="fas fa-info-circle"></i>
                        {{ message }}
                        <button class="flash-close" onclick="this.parentElement.remove()">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                {% endfor %}
            </div>
        {% endif %}
    {% endwith %}
    
    <!-- Main content -->
    <main class="main-content">
        {% block content %}{% endblock %}
    </main>
    
    <!-- Footer -->
    <footer class="footer">
        <div class="footer-content">
            <p>&copy; 2024 Galaxy Mentor. Guiding you through the cosmos of knowledge.</p>
            <div class="footer-links">
                <span><i class="fas fa-code"></i> Built with Flask & OpenAI</span>
            </div>
        </div>
    </footer>
    
    <!-- JavaScript -->
    <script src="{{ url_for('static', filename='js/main.js') }}"></script>
    {% block scripts %}{% endblock %}
</body>
</html>
