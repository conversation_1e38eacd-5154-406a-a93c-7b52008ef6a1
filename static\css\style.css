/* Galaxy Mentor - CSS Styles */

/* ===== RESET & BASE STYLES ===== */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    /* Galaxy Color Palette */
    --primary-bg: #0a0a0f;
    --secondary-bg: #1a1a2e;
    --tertiary-bg: #16213e;
    --accent-bg: #0f3460;
    
    --primary-purple: #6c5ce7;
    --secondary-purple: #a29bfe;
    --primary-blue: #74b9ff;
    --secondary-blue: #0984e3;
    --primary-pink: #fd79a8;
    --secondary-pink: #e84393;
    
    --text-primary: #ffffff;
    --text-secondary: #b2bec3;
    --text-muted: #636e72;
    
    --border-color: #2d3748;
    --border-light: #4a5568;
    
    --success: #00b894;
    --warning: #fdcb6e;
    --error: #e17055;
    --info: #74b9ff;
    
    /* Gradients */
    --gradient-primary: linear-gradient(135deg, var(--primary-purple), var(--primary-blue));
    --gradient-secondary: linear-gradient(135deg, var(--secondary-purple), var(--secondary-blue));
    --gradient-accent: linear-gradient(135deg, var(--primary-pink), var(--primary-purple));
    --gradient-bg: linear-gradient(135deg, var(--primary-bg), var(--secondary-bg));
    
    /* Fonts */
    --font-primary: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
    --font-heading: 'Space Grotesk', var(--font-primary);
    
    /* Spacing */
    --spacing-xs: 0.25rem;
    --spacing-sm: 0.5rem;
    --spacing-md: 1rem;
    --spacing-lg: 1.5rem;
    --spacing-xl: 2rem;
    --spacing-2xl: 3rem;
    
    /* Border Radius */
    --radius-sm: 0.375rem;
    --radius-md: 0.5rem;
    --radius-lg: 0.75rem;
    --radius-xl: 1rem;
    
    /* Shadows */
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
    --shadow-glow: 0 0 20px rgba(108, 92, 231, 0.3);
}

body {
    font-family: var(--font-primary);
    background: var(--primary-bg);
    color: var(--text-primary);
    line-height: 1.6;
    overflow-x: hidden;
    min-height: 100vh;
}

/* ===== ANIMATED BACKGROUND ===== */
.galaxy-background {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: -1;
    background: var(--gradient-bg);
}

.stars, .stars2, .stars3 {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: transparent;
}

.stars {
    background-image: 
        radial-gradient(2px 2px at 20px 30px, #eee, transparent),
        radial-gradient(2px 2px at 40px 70px, rgba(255,255,255,0.8), transparent),
        radial-gradient(1px 1px at 90px 40px, #fff, transparent),
        radial-gradient(1px 1px at 130px 80px, rgba(255,255,255,0.6), transparent),
        radial-gradient(2px 2px at 160px 30px, #ddd, transparent);
    background-repeat: repeat;
    background-size: 200px 100px;
    animation: zoom 20s infinite linear;
}

.stars2 {
    background-image: 
        radial-gradient(1px 1px at 40px 60px, #fff, transparent),
        radial-gradient(1px 1px at 120px 10px, rgba(255,255,255,0.7), transparent),
        radial-gradient(1px 1px at 160px 90px, #eee, transparent);
    background-repeat: repeat;
    background-size: 300px 150px;
    animation: zoom 30s infinite linear;
}

.stars3 {
    background-image: 
        radial-gradient(1px 1px at 10px 10px, rgba(255,255,255,0.5), transparent),
        radial-gradient(1px 1px at 150px 150px, rgba(255,255,255,0.3), transparent);
    background-repeat: repeat;
    background-size: 400px 200px;
    animation: zoom 40s infinite linear;
}

@keyframes zoom {
    from {
        transform: scale(0);
    }
    to {
        transform: scale(1);
    }
}

/* ===== NAVIGATION ===== */
.navbar {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    background: rgba(26, 26, 46, 0.9);
    backdrop-filter: blur(10px);
    border-bottom: 1px solid var(--border-color);
    padding: var(--spacing-md) 0;
}

.nav-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 var(--spacing-lg);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.nav-brand {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    font-family: var(--font-heading);
    font-weight: 600;
    font-size: 1.25rem;
    color: var(--text-primary);
    text-decoration: none;
}

.nav-brand i {
    color: var(--primary-purple);
    font-size: 1.5rem;
}

.nav-menu {
    display: flex;
    align-items: center;
    gap: var(--spacing-lg);
}

.nav-user {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    color: var(--text-secondary);
    font-size: 0.9rem;
}

.nav-user i {
    color: var(--primary-blue);
}

.nav-link {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    color: var(--text-secondary);
    text-decoration: none;
    padding: var(--spacing-sm) var(--spacing-md);
    border-radius: var(--radius-md);
    transition: all 0.3s ease;
    font-size: 0.9rem;
}

.nav-link:hover {
    color: var(--text-primary);
    background: rgba(255, 255, 255, 0.1);
}

.nav-cta {
    background: var(--gradient-primary);
    color: var(--text-primary) !important;
    font-weight: 500;
}

.nav-cta:hover {
    background: var(--gradient-secondary);
    transform: translateY(-1px);
    box-shadow: var(--shadow-glow);
}

/* ===== MAIN CONTENT ===== */
.main-content {
    margin-top: 80px;
    min-height: calc(100vh - 80px);
}

/* ===== FLASH MESSAGES ===== */
.flash-container {
    position: fixed;
    top: 100px;
    right: var(--spacing-lg);
    z-index: 1001;
    max-width: 400px;
}

.flash-message {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-md);
    margin-bottom: var(--spacing-sm);
    border-radius: var(--radius-md);
    backdrop-filter: blur(10px);
    animation: slideIn 0.3s ease;
}

.flash-success {
    background: rgba(0, 184, 148, 0.2);
    border: 1px solid var(--success);
    color: var(--success);
}

.flash-error {
    background: rgba(225, 112, 85, 0.2);
    border: 1px solid var(--error);
    color: var(--error);
}

.flash-warning {
    background: rgba(253, 203, 110, 0.2);
    border: 1px solid var(--warning);
    color: var(--warning);
}

.flash-info {
    background: rgba(116, 185, 255, 0.2);
    border: 1px solid var(--info);
    color: var(--info);
}

.flash-close {
    background: none;
    border: none;
    color: inherit;
    cursor: pointer;
    padding: var(--spacing-xs);
    margin-left: auto;
    border-radius: var(--radius-sm);
    transition: background 0.2s ease;
}

.flash-close:hover {
    background: rgba(255, 255, 255, 0.1);
}

@keyframes slideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* ===== BUTTONS ===== */
.btn {
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-sm) var(--spacing-lg);
    border: none;
    border-radius: var(--radius-md);
    font-family: var(--font-primary);
    font-weight: 500;
    text-decoration: none;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.btn-primary {
    background: var(--gradient-primary);
    color: var(--text-primary);
}

.btn-primary:hover {
    background: var(--gradient-secondary);
    transform: translateY(-2px);
    box-shadow: var(--shadow-glow);
}

.btn-secondary {
    background: rgba(255, 255, 255, 0.1);
    color: var(--text-primary);
    border: 1px solid var(--border-light);
}

.btn-secondary:hover {
    background: rgba(255, 255, 255, 0.2);
    border-color: var(--primary-purple);
}

.btn-full {
    width: 100%;
    justify-content: center;
}

.btn-large {
    padding: var(--spacing-md) var(--spacing-xl);
    font-size: 1.1rem;
}

.btn-icon {
    background: none;
    border: none;
    color: var(--text-secondary);
    cursor: pointer;
    padding: var(--spacing-sm);
    border-radius: var(--radius-sm);
    transition: all 0.2s ease;
}

.btn-icon:hover {
    color: var(--text-primary);
    background: rgba(255, 255, 255, 0.1);
}

.btn-send {
    background: var(--gradient-primary);
    border: none;
    color: var(--text-primary);
    cursor: pointer;
    padding: var(--spacing-sm);
    border-radius: var(--radius-md);
    transition: all 0.2s ease;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.btn-send:hover:not(:disabled) {
    background: var(--gradient-secondary);
    transform: scale(1.05);
}

.btn-send:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.btn-loading {
    display: none;
}

.btn.loading .btn-loading {
    display: inline-flex;
}

.btn.loading span {
    display: none;
}

/* ===== HERO SECTION ===== */
.hero-section {
    padding: var(--spacing-2xl) var(--spacing-lg);
    min-height: calc(100vh - 80px);
    display: flex;
    align-items: center;
}

.hero-content {
    max-width: 1200px;
    margin: 0 auto;
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-2xl);
    align-items: center;
}

.hero-title {
    font-family: var(--font-heading);
    font-size: 3.5rem;
    font-weight: 700;
    line-height: 1.2;
    margin-bottom: var(--spacing-lg);
}

.gradient-text {
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.hero-subtitle {
    font-size: 1.25rem;
    color: var(--text-secondary);
    margin-bottom: var(--spacing-xl);
    line-height: 1.6;
}

.hero-features {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-xl);
}

.feature {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    color: var(--text-secondary);
}

.feature i {
    color: var(--primary-purple);
    font-size: 1.2rem;
}

.hero-actions {
    display: flex;
    gap: var(--spacing-md);
    flex-wrap: wrap;
}

.hero-visual {
    display: flex;
    justify-content: center;
    align-items: center;
}

.floating-card {
    background: rgba(26, 26, 46, 0.8);
    backdrop-filter: blur(10px);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-xl);
    padding: var(--spacing-lg);
    max-width: 400px;
    animation: float 6s ease-in-out infinite;
}

@keyframes float {
    0%, 100% {
        transform: translateY(0px);
    }
    50% {
        transform: translateY(-20px);
    }
}

.card-header {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-lg);
}

.card-avatar {
    width: 50px;
    height: 50px;
    background: var(--gradient-primary);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
}

.card-info h4 {
    font-weight: 600;
    margin-bottom: var(--spacing-xs);
}

.status-online {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    color: var(--success);
    font-size: 0.9rem;
}

.status-online i {
    font-size: 0.5rem;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0%, 100% {
        opacity: 1;
    }
    50% {
        opacity: 0.5;
    }
}

.card-content .message {
    background: rgba(255, 255, 255, 0.05);
    padding: var(--spacing-md);
    border-radius: var(--radius-md);
    margin-bottom: var(--spacing-md);
}

.typing-indicator {
    display: flex;
    gap: var(--spacing-xs);
    align-items: center;
}

.typing-indicator span {
    width: 8px;
    height: 8px;
    background: var(--primary-purple);
    border-radius: 50%;
    animation: typing 1.4s infinite ease-in-out;
}

.typing-indicator span:nth-child(2) {
    animation-delay: 0.2s;
}

.typing-indicator span:nth-child(3) {
    animation-delay: 0.4s;
}

@keyframes typing {
    0%, 80%, 100% {
        transform: scale(0.8);
        opacity: 0.5;
    }
    40% {
        transform: scale(1);
        opacity: 1;
    }
}

/* ===== FEATURES SECTION ===== */
.features-section {
    padding: var(--spacing-2xl) var(--spacing-lg);
    background: rgba(26, 26, 46, 0.3);
}

.container {
    max-width: 1200px;
    margin: 0 auto;
}

.section-title {
    font-family: var(--font-heading);
    font-size: 2.5rem;
    font-weight: 600;
    text-align: center;
    margin-bottom: var(--spacing-2xl);
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: var(--spacing-xl);
}

.feature-card {
    background: rgba(26, 26, 46, 0.6);
    backdrop-filter: blur(10px);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-lg);
    padding: var(--spacing-xl);
    text-align: center;
    transition: all 0.3s ease;
}

.feature-card:hover {
    transform: translateY(-5px);
    border-color: var(--primary-purple);
    box-shadow: 0 10px 30px rgba(108, 92, 231, 0.2);
}

.feature-icon {
    width: 60px;
    height: 60px;
    background: var(--gradient-primary);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto var(--spacing-lg);
    font-size: 1.5rem;
}

.feature-card h3 {
    font-family: var(--font-heading);
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: var(--spacing-md);
}

.feature-card p {
    color: var(--text-secondary);
    line-height: 1.6;
}

/* ===== CTA SECTION ===== */
.cta-section {
    padding: var(--spacing-2xl) var(--spacing-lg);
    text-align: center;
}

.cta-content h2 {
    font-family: var(--font-heading);
    font-size: 2.5rem;
    font-weight: 600;
    margin-bottom: var(--spacing-md);
}

.cta-content p {
    font-size: 1.25rem;
    color: var(--text-secondary);
    margin-bottom: var(--spacing-xl);
}

/* ===== AUTHENTICATION ===== */
.auth-container {
    min-height: calc(100vh - 80px);
    display: grid;
    grid-template-columns: 1fr 1fr;
    align-items: center;
}

.auth-card {
    max-width: 450px;
    margin: 0 auto;
    padding: var(--spacing-xl);
}

.auth-header {
    text-align: center;
    margin-bottom: var(--spacing-xl);
}

.auth-icon {
    width: 80px;
    height: 80px;
    background: var(--gradient-primary);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto var(--spacing-lg);
    font-size: 2rem;
}

.auth-header h2 {
    font-family: var(--font-heading);
    font-size: 2rem;
    font-weight: 600;
    margin-bottom: var(--spacing-sm);
}

.auth-header p {
    color: var(--text-secondary);
    font-size: 1.1rem;
}

.auth-form {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-lg);
}

.form-group {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
}

.form-group label {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    font-weight: 500;
    color: var(--text-primary);
}

.form-group label i {
    color: var(--primary-purple);
    width: 16px;
}

.form-group input {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    padding: var(--spacing-md);
    color: var(--text-primary);
    font-family: var(--font-primary);
    transition: all 0.3s ease;
}

.form-group input:focus {
    outline: none;
    border-color: var(--primary-purple);
    box-shadow: 0 0 0 3px rgba(108, 92, 231, 0.1);
}

.form-group input::placeholder {
    color: var(--text-muted);
}

.password-input {
    position: relative;
}

.password-toggle {
    position: absolute;
    right: var(--spacing-md);
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    color: var(--text-muted);
    cursor: pointer;
    padding: var(--spacing-xs);
    border-radius: var(--radius-sm);
    transition: color 0.2s ease;
}

.password-toggle:hover {
    color: var(--text-secondary);
}

.form-hint {
    font-size: 0.875rem;
    color: var(--text-muted);
}

.form-options {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.checkbox-label {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    cursor: pointer;
    font-size: 0.9rem;
}

.checkbox-label input[type="checkbox"] {
    display: none;
}

.checkmark {
    width: 18px;
    height: 18px;
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-sm);
    position: relative;
    transition: all 0.3s ease;
}

.checkbox-label input[type="checkbox"]:checked + .checkmark {
    background: var(--gradient-primary);
    border-color: var(--primary-purple);
}

.checkbox-label input[type="checkbox"]:checked + .checkmark::after {
    content: '✓';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: white;
    font-size: 12px;
    font-weight: bold;
}

.auth-footer {
    text-align: center;
    margin-top: var(--spacing-xl);
    padding-top: var(--spacing-lg);
    border-top: 1px solid var(--border-color);
}

.auth-footer p {
    color: var(--text-secondary);
    margin-bottom: var(--spacing-sm);
}

.auth-link {
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-sm);
    color: var(--primary-purple);
    text-decoration: none;
    font-weight: 500;
    transition: color 0.3s ease;
}

.auth-link:hover {
    color: var(--secondary-purple);
}

.auth-visual {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: var(--spacing-xl);
    background: rgba(26, 26, 46, 0.3);
}

.visual-content {
    max-width: 400px;
    text-align: center;
}

.visual-content h3 {
    font-family: var(--font-heading);
    font-size: 1.5rem;
    margin-bottom: var(--spacing-xl);
}

.benefits {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-xl);
}

.benefit {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    color: var(--text-secondary);
}

.benefit i {
    color: var(--primary-purple);
    font-size: 1.2rem;
    width: 20px;
}

.journey-stats {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: var(--spacing-lg);
    margin-bottom: var(--spacing-xl);
}

.stat {
    text-align: center;
}

.stat-number {
    font-family: var(--font-heading);
    font-size: 2rem;
    font-weight: 700;
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.stat-label {
    color: var(--text-secondary);
    font-size: 0.9rem;
    margin-top: var(--spacing-xs);
}

.testimonial {
    background: rgba(255, 255, 255, 0.05);
    padding: var(--spacing-lg);
    border-radius: var(--radius-lg);
    border-left: 3px solid var(--primary-purple);
}

.testimonial p {
    font-style: italic;
    margin-bottom: var(--spacing-md);
    line-height: 1.6;
}

.testimonial-author {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    color: var(--text-secondary);
    font-size: 0.9rem;
}

.testimonial-author i {
    color: var(--primary-blue);
    font-size: 1.2rem;
}

/* ===== CHAT INTERFACE ===== */
.chat-container {
    height: calc(100vh - 80px);
    display: flex;
    flex-direction: column;
    max-width: 1000px;
    margin: 0 auto;
    background: rgba(26, 26, 46, 0.3);
    backdrop-filter: blur(10px);
    border-left: 1px solid var(--border-color);
    border-right: 1px solid var(--border-color);
}

.chat-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-lg);
    border-bottom: 1px solid var(--border-color);
    background: rgba(26, 26, 46, 0.8);
}

.chat-info {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
}

.mentor-avatar {
    position: relative;
    width: 50px;
    height: 50px;
    background: var(--gradient-primary);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
}

.status-indicator {
    position: absolute;
    bottom: 2px;
    right: 2px;
    width: 12px;
    height: 12px;
    background: var(--success);
    border: 2px solid var(--secondary-bg);
    border-radius: 50%;
    animation: pulse 2s infinite;
}

.mentor-details h3 {
    font-family: var(--font-heading);
    font-weight: 600;
    margin-bottom: var(--spacing-xs);
}

.mentor-status {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    color: var(--success);
    font-size: 0.9rem;
}

.mentor-status i {
    font-size: 0.5rem;
}

.chat-actions {
    display: flex;
    gap: var(--spacing-sm);
}

.chat-messages {
    flex: 1;
    overflow-y: auto;
    padding: var(--spacing-lg);
    display: flex;
    flex-direction: column;
    gap: var(--spacing-lg);
}

.message {
    display: flex;
    gap: var(--spacing-md);
    max-width: 80%;
    animation: messageSlide 0.3s ease;
}

@keyframes messageSlide {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.user-message {
    align-self: flex-end;
    flex-direction: row-reverse;
}

.bot-message {
    align-self: flex-start;
}

.message-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
    flex-shrink: 0;
}

.user-message .message-avatar {
    background: var(--gradient-accent);
}

.bot-message .message-avatar {
    background: var(--gradient-primary);
}

.message-content {
    flex: 1;
}

.message-text {
    background: rgba(255, 255, 255, 0.05);
    padding: var(--spacing-md);
    border-radius: var(--radius-lg);
    line-height: 1.6;
    word-wrap: break-word;
}

.user-message .message-text {
    background: var(--gradient-accent);
    color: var(--text-primary);
}

.bot-message .message-text {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid var(--border-color);
}

.message-time {
    font-size: 0.8rem;
    color: var(--text-muted);
    margin-top: var(--spacing-xs);
    text-align: right;
}

.user-message .message-time {
    text-align: left;
}

.typing-message {
    opacity: 0.8;
}

/* ===== CHAT INPUT ===== */
.chat-input-container {
    padding: var(--spacing-lg);
    border-top: 1px solid var(--border-color);
    background: rgba(26, 26, 46, 0.8);
}

.chat-form {
    max-width: 100%;
}

.input-wrapper {
    display: flex;
    gap: var(--spacing-md);
    align-items: flex-end;
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-lg);
    padding: var(--spacing-md);
    transition: border-color 0.3s ease;
}

.input-wrapper:focus-within {
    border-color: var(--primary-purple);
    box-shadow: 0 0 0 3px rgba(108, 92, 231, 0.1);
}

.input-wrapper textarea {
    flex: 1;
    background: none;
    border: none;
    color: var(--text-primary);
    font-family: var(--font-primary);
    font-size: 1rem;
    line-height: 1.5;
    resize: none;
    outline: none;
    min-height: 24px;
    max-height: 120px;
}

.input-wrapper textarea::placeholder {
    color: var(--text-muted);
}

.input-actions {
    display: flex;
    gap: var(--spacing-sm);
    align-items: center;
}

.input-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: var(--spacing-sm);
    font-size: 0.875rem;
    color: var(--text-muted);
}

.char-counter {
    font-size: 0.8rem;
}

.input-hints {
    font-size: 0.8rem;
}

.hint {
    opacity: 0.7;
}

/* ===== CHAT HISTORY SIDEBAR ===== */
.chat-history-sidebar {
    position: fixed;
    top: 80px;
    right: -400px;
    width: 400px;
    height: calc(100vh - 80px);
    background: rgba(26, 26, 46, 0.95);
    backdrop-filter: blur(10px);
    border-left: 1px solid var(--border-color);
    transition: right 0.3s ease;
    z-index: 1001;
    display: flex;
    flex-direction: column;
}

.chat-history-sidebar.active {
    right: 0;
}

.sidebar-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-lg);
    border-bottom: 1px solid var(--border-color);
}

.sidebar-header h3 {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    font-family: var(--font-heading);
    font-weight: 600;
}

.sidebar-content {
    flex: 1;
    overflow-y: auto;
    padding: var(--spacing-lg);
}

.history-loading {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-xl);
    color: var(--text-secondary);
}

.history-list {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
}

.history-item {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    padding: var(--spacing-md);
    transition: all 0.2s ease;
}

.history-item:hover {
    border-color: var(--primary-purple);
    background: rgba(255, 255, 255, 0.08);
}

.history-message {
    margin-bottom: var(--spacing-sm);
    font-size: 0.9rem;
    line-height: 1.4;
}

.history-message.user {
    color: var(--text-primary);
}

.history-message.bot {
    color: var(--text-secondary);
}

.history-message strong {
    color: var(--primary-purple);
}

.history-time {
    font-size: 0.8rem;
    color: var(--text-muted);
    text-align: right;
}

.no-history, .history-error {
    text-align: center;
    color: var(--text-muted);
    padding: var(--spacing-xl);
    font-style: italic;
}

.sidebar-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    z-index: 1000;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.sidebar-overlay.active {
    opacity: 1;
    visibility: visible;
}

/* ===== FOOTER ===== */
.footer {
    background: rgba(26, 26, 46, 0.8);
    border-top: 1px solid var(--border-color);
    padding: var(--spacing-lg) 0;
    margin-top: auto;
}

.footer-content {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 var(--spacing-lg);
    display: flex;
    justify-content: space-between;
    align-items: center;
    color: var(--text-secondary);
    font-size: 0.9rem;
}

.footer-links {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.footer-links i {
    color: var(--primary-purple);
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 1024px) {
    .hero-content {
        grid-template-columns: 1fr;
        text-align: center;
    }

    .hero-title {
        font-size: 3rem;
    }

    .features-grid {
        grid-template-columns: repeat(2, 1fr);
    }

    .auth-container {
        grid-template-columns: 1fr;
        padding: var(--spacing-lg);
    }

    .auth-visual {
        display: none;
    }

    .chat-history-sidebar {
        width: 350px;
        right: -350px;
    }
}

@media (max-width: 768px) {
    .nav-container {
        padding: 0 var(--spacing-md);
    }

    .nav-menu {
        gap: var(--spacing-md);
    }

    .nav-user {
        display: none;
    }

    .hero-title {
        font-size: 2.5rem;
    }

    .hero-subtitle {
        font-size: 1.1rem;
    }

    .hero-features {
        grid-template-columns: 1fr;
    }

    .hero-actions {
        flex-direction: column;
        align-items: center;
    }

    .features-grid {
        grid-template-columns: 1fr;
    }

    .section-title {
        font-size: 2rem;
    }

    .cta-content h2 {
        font-size: 2rem;
    }

    .cta-content p {
        font-size: 1.1rem;
    }

    .chat-container {
        border-left: none;
        border-right: none;
    }

    .chat-header {
        padding: var(--spacing-md);
    }

    .chat-messages {
        padding: var(--spacing-md);
    }

    .message {
        max-width: 90%;
    }

    .chat-input-container {
        padding: var(--spacing-md);
    }

    .chat-history-sidebar {
        width: 100%;
        right: -100%;
    }

    .footer-content {
        flex-direction: column;
        gap: var(--spacing-sm);
        text-align: center;
    }
}

@media (max-width: 480px) {
    .hero-section {
        padding: var(--spacing-xl) var(--spacing-md);
    }

    .hero-title {
        font-size: 2rem;
    }

    .floating-card {
        max-width: 300px;
        padding: var(--spacing-md);
    }

    .auth-card {
        padding: var(--spacing-lg);
    }

    .auth-header h2 {
        font-size: 1.5rem;
    }

    .btn-large {
        padding: var(--spacing-md) var(--spacing-lg);
        font-size: 1rem;
    }

    .flash-container {
        right: var(--spacing-md);
        left: var(--spacing-md);
        max-width: none;
    }
}

/* ===== UTILITY CLASSES ===== */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.mt-0 { margin-top: 0; }
.mt-1 { margin-top: var(--spacing-sm); }
.mt-2 { margin-top: var(--spacing-md); }
.mt-3 { margin-top: var(--spacing-lg); }

.mb-0 { margin-bottom: 0; }
.mb-1 { margin-bottom: var(--spacing-sm); }
.mb-2 { margin-bottom: var(--spacing-md); }
.mb-3 { margin-bottom: var(--spacing-lg); }

.hidden { display: none; }
.visible { display: block; }

/* ===== SCROLLBAR STYLING ===== */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: var(--secondary-bg);
}

::-webkit-scrollbar-thumb {
    background: var(--border-light);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--primary-purple);
}
