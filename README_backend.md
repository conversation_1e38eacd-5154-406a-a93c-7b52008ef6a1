# 🤖 Flask Chatbot Backend with OpenAI API

A robust Flask backend for a dynamic chatbot powered by OpenAI's GPT models. Features comprehensive error handling, logging, and RESTful API endpoints.

## ✨ Features

- **🔑 Environment Variable Support**: Secure API key management
- **🛡️ Comprehensive Error Handling**: Graceful handling of API failures
- **📝 Console Logging**: Detailed logging for debugging and monitoring
- **🌐 RESTful API**: Clean endpoints for frontend integration
- **🔄 CORS Support**: Cross-origin requests enabled
- **⚙️ Configurable**: Adjustable model parameters
- **📊 Usage Tracking**: Token usage monitoring
- **🕒 Conversation History**: Context-aware responses

## 🚀 Quick Start

### Prerequisites

- Python 3.8+
- OpenAI API key ([Get one here](https://platform.openai.com/api-keys))

### Installation

1. **Install dependencies**:
   ```bash
   pip install -r requirements_backend.txt
   ```

2. **Configure environment**:
   ```bash
   cp .env_backend_template .env
   # Edit .env with your OpenAI API key
   ```

3. **Run the server**:
   ```bash
   python chatbot_backend.py
   ```

4. **Test the API**:
   ```bash
   python test_chatbot_client.py
   ```

## 📡 API Endpoints

### Health Check
```http
GET /
```
Returns server status and health information.

### Chat Endpoint
```http
POST /api/chat
Content-Type: application/json

{
  "message": "Hello, how are you?",
  "conversation_history": [
    {
      "user": "Previous message",
      "assistant": "Previous response"
    }
  ]
}
```

**Response**:
```json
{
  "success": true,
  "message": "I'm doing well, thank you! How can I help you today?",
  "model": "gpt-3.5-turbo",
  "tokens_used": 45,
  "timestamp": "2024-01-01T12:00:00.000000"
}
```

### Get Available Models
```http
GET /api/chat/models
```

### Configuration Management
```http
GET /api/chat/config
POST /api/chat/config
Content-Type: application/json

{
  "model": "gpt-3.5-turbo",
  "max_tokens": 500,
  "temperature": 0.7
}
```

## 🔧 Configuration

### Environment Variables

| Variable | Description | Default |
|----------|-------------|---------|
| `OPENAI_API_KEY` | Your OpenAI API key | Required |
| `FLASK_SECRET_KEY` | Flask secret key | `dev-secret-key` |
| `FLASK_ENV` | Environment mode | `development` |
| `PORT` | Server port | `5000` |
| `LOG_LEVEL` | Logging level | `INFO` |

### Model Parameters

- **Model**: `gpt-3.5-turbo` (default) or `gpt-4`
- **Max Tokens**: `500` (adjustable)
- **Temperature**: `0.7` (creativity level)
- **Presence Penalty**: `0.1` (topic diversity)
- **Frequency Penalty**: `0.1` (repetition reduction)

## 🛡️ Error Handling

The backend handles various error scenarios:

- **Authentication Errors**: Invalid API key
- **Rate Limiting**: API quota exceeded
- **Network Issues**: Connection timeouts
- **Invalid Requests**: Malformed input
- **Server Errors**: Unexpected failures

All errors are logged with detailed information for debugging.

## 📝 Logging

Logs are written to both console and `chatbot.log` file:

```
2024-01-01 12:00:00,000 - __main__ - INFO - OpenAI client initialized successfully
2024-01-01 12:00:01,000 - __main__ - INFO - Processing chat request. Message length: 25
2024-01-01 12:00:02,000 - __main__ - INFO - OpenAI response received successfully. Length: 150 characters
```

## 🧪 Testing

### Interactive Test Client

Run the included test client for interactive testing:

```bash
python test_chatbot_client.py
```

### API Testing with curl

```bash
# Health check
curl http://localhost:5000/

# Send a message
curl -X POST http://localhost:5000/api/chat \
  -H "Content-Type: application/json" \
  -d '{"message": "Hello, world!"}'

# Get models
curl http://localhost:5000/api/chat/models

# Get configuration
curl http://localhost:5000/api/chat/config
```

## 🔒 Security Best Practices

1. **Environment Variables**: Never commit API keys to version control
2. **HTTPS**: Use HTTPS in production
3. **Rate Limiting**: Implement rate limiting for production use
4. **Input Validation**: All inputs are validated and sanitized
5. **Error Messages**: Sensitive information is not exposed in error messages

## 🚀 Production Deployment

### Using Gunicorn

```bash
pip install gunicorn
gunicorn -w 4 -b 0.0.0.0:5000 chatbot_backend:app
```

### Docker Deployment

```dockerfile
FROM python:3.9-slim

WORKDIR /app
COPY requirements_backend.txt .
RUN pip install -r requirements_backend.txt

COPY . .
EXPOSE 5000

CMD ["gunicorn", "-w", "4", "-b", "0.0.0.0:5000", "chatbot_backend:app"]
```

## 📊 Monitoring

The backend provides several monitoring capabilities:

- **Health Check Endpoint**: Monitor service availability
- **Request Logging**: Track all API requests
- **Error Logging**: Detailed error information
- **Token Usage**: Monitor OpenAI API usage
- **Response Times**: Track performance metrics

## 🤝 Integration Examples

### JavaScript Frontend

```javascript
async function sendMessage(message) {
  const response = await fetch('http://localhost:5000/api/chat', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      message: message,
      conversation_history: conversationHistory
    })
  });
  
  const data = await response.json();
  return data;
}
```

### Python Client

```python
import requests

def chat_with_bot(message, history=[]):
    response = requests.post('http://localhost:5000/api/chat', json={
        'message': message,
        'conversation_history': history
    })
    return response.json()
```

## 📈 Performance Optimization

- **Connection Pooling**: Reuse HTTP connections
- **Caching**: Cache frequent responses
- **Async Processing**: Handle multiple requests concurrently
- **Load Balancing**: Distribute requests across multiple instances

## 🐛 Troubleshooting

### Common Issues

1. **API Key Error**: Ensure `OPENAI_API_KEY` is set correctly
2. **Connection Timeout**: Check internet connection and OpenAI service status
3. **Rate Limiting**: Implement exponential backoff for retries
4. **Memory Usage**: Monitor conversation history size

### Debug Mode

Enable debug mode for detailed error information:

```bash
export FLASK_ENV=development
python chatbot_backend.py
```

## 📄 License

This project is open source and available under the MIT License.

---

**Built with ❤️ using Flask and OpenAI API** 🚀
