"""
Test Client for Chatbot Backend
Demonstrates how to interact with the Flask chatbot API
"""

import requests
import json
from datetime import datetime

class ChatbotClient:
    """Client for interacting with the chatbot backend"""
    
    def __init__(self, base_url="http://localhost:5000"):
        self.base_url = base_url
        self.conversation_history = []
    
    def health_check(self):
        """Check if the backend is running"""
        try:
            response = requests.get(f"{self.base_url}/")
            return response.json()
        except requests.exceptions.RequestException as e:
            return {"error": f"Connection failed: {str(e)}"}
    
    def send_message(self, message):
        """Send a message to the chatbot"""
        try:
            payload = {
                "message": message,
                "conversation_history": self.conversation_history
            }
            
            response = requests.post(
                f"{self.base_url}/api/chat",
                json=payload,
                headers={"Content-Type": "application/json"}
            )
            
            result = response.json()
            
            # If successful, add to conversation history
            if result.get('success'):
                self.conversation_history.append({
                    'user': message,
                    'assistant': result['message']
                })
            
            return result
            
        except requests.exceptions.RequestException as e:
            return {"error": f"Request failed: {str(e)}"}
    
    def get_models(self):
        """Get available models"""
        try:
            response = requests.get(f"{self.base_url}/api/chat/models")
            return response.json()
        except requests.exceptions.RequestException as e:
            return {"error": f"Request failed: {str(e)}"}
    
    def get_config(self):
        """Get current configuration"""
        try:
            response = requests.get(f"{self.base_url}/api/chat/config")
            return response.json()
        except requests.exceptions.RequestException as e:
            return {"error": f"Request failed: {str(e)}"}
    
    def update_config(self, config):
        """Update chatbot configuration"""
        try:
            response = requests.post(
                f"{self.base_url}/api/chat/config",
                json=config,
                headers={"Content-Type": "application/json"}
            )
            return response.json()
        except requests.exceptions.RequestException as e:
            return {"error": f"Request failed: {str(e)}"}
    
    def clear_history(self):
        """Clear conversation history"""
        self.conversation_history = []

def main():
    """Interactive test client"""
    print("🤖 Chatbot Backend Test Client")
    print("=" * 40)
    
    client = ChatbotClient()
    
    # Health check
    print("Checking backend health...")
    health = client.health_check()
    if 'error' in health:
        print(f"❌ Backend not available: {health['error']}")
        return
    else:
        print(f"✅ Backend is healthy: {health.get('status', 'unknown')}")
    
    # Get configuration
    print("\nCurrent configuration:")
    config = client.get_config()
    if config.get('success'):
        for key, value in config['config'].items():
            print(f"  {key}: {value}")
    
    print("\n" + "=" * 40)
    print("Interactive Chat (type 'quit' to exit, 'clear' to clear history)")
    print("=" * 40)
    
    while True:
        try:
            user_input = input("\n👤 You: ").strip()
            
            if user_input.lower() in ['quit', 'exit', 'q']:
                print("👋 Goodbye!")
                break
            
            if user_input.lower() == 'clear':
                client.clear_history()
                print("🧹 Conversation history cleared!")
                continue
            
            if not user_input:
                continue
            
            print("🤖 Bot: Thinking...")
            
            # Send message
            response = client.send_message(user_input)
            
            if response.get('success'):
                print(f"🤖 Bot: {response['message']}")
                
                # Show metadata
                if response.get('tokens_used'):
                    print(f"📊 Tokens used: {response['tokens_used']}")
            else:
                print(f"❌ Error: {response.get('error', 'Unknown error')}")
                
        except KeyboardInterrupt:
            print("\n👋 Goodbye!")
            break
        except Exception as e:
            print(f"❌ Unexpected error: {str(e)}")

if __name__ == "__main__":
    main()
