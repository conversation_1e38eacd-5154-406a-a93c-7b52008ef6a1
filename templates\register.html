{% extends "base.html" %}

{% block title %}Sign Up - Galaxy Mentor{% endblock %}

{% block content %}
<div class="auth-container">
    <div class="auth-card">
        <div class="auth-header">
            <div class="auth-icon">
                <i class="fas fa-user-astronaut"></i>
            </div>
            <h2>Join the Galaxy</h2>
            <p>Create your account and start your journey with Galaxy Mentor</p>
        </div>
        
        <form class="auth-form" method="POST" id="registerForm">
            <div class="form-group">
                <label for="username">
                    <i class="fas fa-user"></i>
                    Username
                </label>
                <input 
                    type="text" 
                    id="username" 
                    name="username" 
                    required 
                    minlength="3"
                    placeholder="Choose a unique username"
                    autocomplete="username"
                >
                <div class="form-hint">At least 3 characters</div>
            </div>
            
            <div class="form-group">
                <label for="email">
                    <i class="fas fa-envelope"></i>
                    Email Address
                </label>
                <input 
                    type="email" 
                    id="email" 
                    name="email" 
                    required 
                    placeholder="<EMAIL>"
                    autocomplete="email"
                >
            </div>
            
            <div class="form-group">
                <label for="password">
                    <i class="fas fa-lock"></i>
                    Password
                </label>
                <div class="password-input">
                    <input 
                        type="password" 
                        id="password" 
                        name="password" 
                        required 
                        minlength="6"
                        placeholder="Create a secure password"
                        autocomplete="new-password"
                    >
                    <button type="button" class="password-toggle" onclick="togglePassword('password')">
                        <i class="fas fa-eye"></i>
                    </button>
                </div>
                <div class="form-hint">At least 6 characters</div>
            </div>
            
            <div class="form-group">
                <label for="confirmPassword">
                    <i class="fas fa-lock"></i>
                    Confirm Password
                </label>
                <div class="password-input">
                    <input 
                        type="password" 
                        id="confirmPassword" 
                        name="confirmPassword" 
                        required 
                        placeholder="Confirm your password"
                        autocomplete="new-password"
                    >
                    <button type="button" class="password-toggle" onclick="togglePassword('confirmPassword')">
                        <i class="fas fa-eye"></i>
                    </button>
                </div>
            </div>
            
            <button type="submit" class="btn btn-primary btn-full">
                <i class="fas fa-rocket"></i>
                <span>Launch Into the Galaxy</span>
                <div class="btn-loading">
                    <i class="fas fa-spinner fa-spin"></i>
                </div>
            </button>
        </form>
        
        <div class="auth-footer">
            <p>Already have an account?</p>
            <a href="{{ url_for('login') }}" class="auth-link">
                <i class="fas fa-sign-in-alt"></i>
                Sign In
            </a>
        </div>
    </div>
    
    <div class="auth-visual">
        <div class="visual-content">
            <h3>🌌 Welcome to the Cosmic Community</h3>
            <div class="benefits">
                <div class="benefit">
                    <i class="fas fa-brain"></i>
                    <span>Personalized growth guidance</span>
                </div>
                <div class="benefit">
                    <i class="fas fa-chart-line"></i>
                    <span>Productivity enhancement</span>
                </div>
                <div class="benefit">
                    <i class="fas fa-code"></i>
                    <span>Programming mentorship</span>
                </div>
                <div class="benefit">
                    <i class="fas fa-lightbulb"></i>
                    <span>Tech insights & tips</span>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script src="{{ url_for('static', filename='js/auth.js') }}"></script>
{% endblock %}
