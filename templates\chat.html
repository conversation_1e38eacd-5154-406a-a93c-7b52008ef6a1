{% extends "base.html" %}

{% block title %}Chat - Galaxy Mentor{% endblock %}

{% block content %}
<div class="chat-container">
    <!-- Chat Header -->
    <div class="chat-header">
        <div class="chat-info">
            <div class="mentor-avatar">
                <i class="fas fa-robot"></i>
                <div class="status-indicator"></div>
            </div>
            <div class="mentor-details">
                <h3>Galaxy Mentor</h3>
                <p class="mentor-status">
                    <i class="fas fa-circle"></i>
                    Ready to guide you
                </p>
            </div>
        </div>
        
        <div class="chat-actions">
            <button class="btn-icon" onclick="clearChat()" title="Clear Chat">
                <i class="fas fa-broom"></i>
            </button>
            <button class="btn-icon" onclick="toggleHistory()" title="Chat History">
                <i class="fas fa-history"></i>
            </button>
        </div>
    </div>
    
    <!-- Chat Messages -->
    <div class="chat-messages" id="chatMessages">
        <div class="welcome-message">
            <div class="message bot-message">
                <div class="message-avatar">
                    <i class="fas fa-robot"></i>
                </div>
                <div class="message-content">
                    <div class="message-text" id="welcomeText">
                        <div class="typing-indicator">
                            <span></span>
                            <span></span>
                            <span></span>
                        </div>
                    </div>
                    <div class="message-time" id="welcomeTime"></div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Chat Input -->
    <div class="chat-input-container">
        <form class="chat-form" id="chatForm">
            <div class="input-wrapper">
                <textarea 
                    id="messageInput" 
                    placeholder="Ask me about self-development, productivity, programming, or tech tips..."
                    rows="1"
                    maxlength="1000"
                ></textarea>
                <div class="input-actions">
                    <button type="button" class="btn-icon" onclick="clearInput()" title="Clear">
                        <i class="fas fa-times"></i>
                    </button>
                    <button type="submit" class="btn-send" id="sendButton" disabled>
                        <i class="fas fa-paper-plane"></i>
                    </button>
                </div>
            </div>
            <div class="input-footer">
                <div class="char-counter">
                    <span id="charCount">0</span>/1000
                </div>
                <div class="input-hints">
                    <span class="hint">Press Enter to send, Shift+Enter for new line</span>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- Chat History Sidebar -->
<div class="chat-history-sidebar" id="chatHistorySidebar">
    <div class="sidebar-header">
        <h3>
            <i class="fas fa-history"></i>
            Chat History
        </h3>
        <button class="btn-icon" onclick="toggleHistory()">
            <i class="fas fa-times"></i>
        </button>
    </div>
    
    <div class="sidebar-content">
        <div class="history-loading">
            <i class="fas fa-spinner fa-spin"></i>
            <span>Loading history...</span>
        </div>
        <div class="history-list" id="historyList">
            <!-- History items will be loaded here -->
        </div>
    </div>
</div>

<!-- Overlay for sidebar -->
<div class="sidebar-overlay" id="sidebarOverlay" onclick="toggleHistory()"></div>
{% endblock %}

{% block scripts %}
<script>
// Chat functionality
const chatMessages = document.getElementById('chatMessages');
const messageInput = document.getElementById('messageInput');
const chatForm = document.getElementById('chatForm');
const sendButton = document.getElementById('sendButton');
const charCount = document.getElementById('charCount');
const welcomeText = document.getElementById('welcomeText');
const welcomeTime = document.getElementById('welcomeTime');

// Initialize chat
document.addEventListener('DOMContentLoaded', function() {
    loadWelcomeMessage();
    setupInputHandlers();
    autoResizeTextarea();
});

// Load welcome message
async function loadWelcomeMessage() {
    try {
        const response = await fetch('/api/welcome');
        const data = await response.json();
        
        // Simulate typing effect
        setTimeout(() => {
            welcomeText.innerHTML = formatMessage(data.message);
            welcomeTime.textContent = formatTime(new Date());
        }, 1500);
        
    } catch (error) {
        console.error('Error loading welcome message:', error);
        welcomeText.innerHTML = '🌌 Welcome! I\'m Galaxy Mentor, ready to help you grow and learn.';
        welcomeTime.textContent = formatTime(new Date());
    }
}

// Setup input handlers
function setupInputHandlers() {
    messageInput.addEventListener('input', function() {
        const length = this.value.length;
        charCount.textContent = length;
        sendButton.disabled = length === 0;
        autoResizeTextarea();
    });
    
    messageInput.addEventListener('keydown', function(e) {
        if (e.key === 'Enter' && !e.shiftKey) {
            e.preventDefault();
            if (!sendButton.disabled) {
                sendMessage();
            }
        }
    });
    
    chatForm.addEventListener('submit', function(e) {
        e.preventDefault();
        sendMessage();
    });
}

// Auto-resize textarea
function autoResizeTextarea() {
    messageInput.style.height = 'auto';
    messageInput.style.height = Math.min(messageInput.scrollHeight, 120) + 'px';
}

// Send message
async function sendMessage() {
    const message = messageInput.value.trim();
    if (!message) return;
    
    // Add user message to chat
    addMessage(message, 'user');
    
    // Clear input and disable send button
    messageInput.value = '';
    charCount.textContent = '0';
    sendButton.disabled = true;
    autoResizeTextarea();
    
    // Add typing indicator
    const typingId = addTypingIndicator();
    
    try {
        const response = await fetch('/api/chat', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ message: message })
        });
        
        const data = await response.json();
        
        // Remove typing indicator
        removeTypingIndicator(typingId);
        
        if (response.ok) {
            // Add bot response
            addMessage(data.response, 'bot', data.timestamp);
        } else {
            addMessage('Sorry, I encountered an error. Please try again.', 'bot');
        }
        
    } catch (error) {
        console.error('Error sending message:', error);
        removeTypingIndicator(typingId);
        addMessage('Sorry, I\'m having trouble connecting. Please try again.', 'bot');
    }
}

// Add message to chat
function addMessage(text, sender, timestamp = null) {
    const messageDiv = document.createElement('div');
    messageDiv.className = `message ${sender}-message`;
    
    const time = timestamp ? new Date(timestamp) : new Date();
    
    messageDiv.innerHTML = `
        <div class="message-avatar">
            <i class="fas fa-${sender === 'user' ? 'user' : 'robot'}"></i>
        </div>
        <div class="message-content">
            <div class="message-text">${formatMessage(text)}</div>
            <div class="message-time">${formatTime(time)}</div>
        </div>
    `;
    
    chatMessages.appendChild(messageDiv);
    scrollToBottom();
}

// Add typing indicator
function addTypingIndicator() {
    const typingDiv = document.createElement('div');
    typingDiv.className = 'message bot-message typing-message';
    typingDiv.id = 'typing-' + Date.now();
    
    typingDiv.innerHTML = `
        <div class="message-avatar">
            <i class="fas fa-robot"></i>
        </div>
        <div class="message-content">
            <div class="message-text">
                <div class="typing-indicator">
                    <span></span>
                    <span></span>
                    <span></span>
                </div>
            </div>
        </div>
    `;
    
    chatMessages.appendChild(typingDiv);
    scrollToBottom();
    
    return typingDiv.id;
}

// Remove typing indicator
function removeTypingIndicator(typingId) {
    const typingElement = document.getElementById(typingId);
    if (typingElement) {
        typingElement.remove();
    }
}

// Format message text
function formatMessage(text) {
    // Convert line breaks to <br>
    text = text.replace(/\n/g, '<br>');
    
    // Convert **bold** to <strong>
    text = text.replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>');
    
    // Convert *italic* to <em>
    text = text.replace(/\*(.*?)\*/g, '<em>$1</em>');
    
    // Convert `code` to <code>
    text = text.replace(/`(.*?)`/g, '<code>$1</code>');
    
    return text;
}

// Format time
function formatTime(date) {
    return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
}

// Scroll to bottom
function scrollToBottom() {
    chatMessages.scrollTop = chatMessages.scrollHeight;
}

// Clear input
function clearInput() {
    messageInput.value = '';
    charCount.textContent = '0';
    sendButton.disabled = true;
    autoResizeTextarea();
    messageInput.focus();
}

// Clear chat
function clearChat() {
    if (confirm('Are you sure you want to clear the chat? This action cannot be undone.')) {
        chatMessages.innerHTML = '';
        loadWelcomeMessage();
    }
}

// Toggle history sidebar
function toggleHistory() {
    const sidebar = document.getElementById('chatHistorySidebar');
    const overlay = document.getElementById('sidebarOverlay');
    
    sidebar.classList.toggle('active');
    overlay.classList.toggle('active');
    
    if (sidebar.classList.contains('active')) {
        loadChatHistory();
    }
}

// Load chat history
async function loadChatHistory() {
    const historyList = document.getElementById('historyList');
    const loading = document.querySelector('.history-loading');
    
    loading.style.display = 'flex';
    historyList.innerHTML = '';
    
    try {
        const response = await fetch('/api/history');
        const data = await response.json();
        
        loading.style.display = 'none';
        
        if (data.history && data.history.length > 0) {
            data.history.forEach(item => {
                const historyItem = document.createElement('div');
                historyItem.className = 'history-item';
                historyItem.innerHTML = `
                    <div class="history-message user">
                        <strong>You:</strong> ${item.user_message.substring(0, 100)}${item.user_message.length > 100 ? '...' : ''}
                    </div>
                    <div class="history-message bot">
                        <strong>Mentor:</strong> ${item.bot_response.substring(0, 100)}${item.bot_response.length > 100 ? '...' : ''}
                    </div>
                    <div class="history-time">${formatTime(new Date(item.timestamp))}</div>
                `;
                historyList.appendChild(historyItem);
            });
        } else {
            historyList.innerHTML = '<div class="no-history">No chat history yet. Start a conversation!</div>';
        }
        
    } catch (error) {
        console.error('Error loading history:', error);
        loading.style.display = 'none';
        historyList.innerHTML = '<div class="history-error">Error loading history</div>';
    }
}
</script>
{% endblock %}
