"""
Flask Backend for Dynamic Chatbot with OpenAI API Integration
Features:
- Environment variable support for API key
- Comprehensive error handling
- Console logging for debugging
- RESTful API endpoints
- CORS support for frontend integration
"""

import os
import logging
from datetime import datetime
from flask import Flask, request, jsonify
from flask_cors import CORS
from dotenv import load_dotenv
from openai import OpenAI
import traceback

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('chatbot.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# Initialize Flask app
app = Flask(__name__)
CORS(app)  # Enable CORS for all routes

# Configuration
app.config['SECRET_KEY'] = os.getenv('FLASK_SECRET_KEY', 'dev-secret-key')
OPENAI_API_KEY = os.getenv('OPENAI_API_KEY')

# Validate OpenAI API key
if not OPENAI_API_KEY:
    logger.error("OPENAI_API_KEY not found in environment variables")
    raise ValueError("OpenAI API key is required. Please set OPENAI_API_KEY in your .env file")

# Initialize OpenAI client
try:
    openai_client = OpenAI(api_key=OPENAI_API_KEY)
    logger.info("OpenAI client initialized successfully")
except Exception as e:
    logger.error(f"Failed to initialize OpenAI client: {str(e)}")
    raise

class ChatbotService:
    """Service class for handling chatbot interactions"""
    
    def __init__(self, client):
        self.client = client
        self.default_model = "gpt-3.5-turbo"
        self.max_tokens = 500
        self.temperature = 0.7
        
        # System prompt for the chatbot personality
        self.system_prompt = """
        You are a helpful, knowledgeable, and friendly AI assistant. 
        You provide clear, accurate, and engaging responses to user questions.
        You can help with a wide variety of topics including:
        - General knowledge and information
        - Problem-solving and advice
        - Creative tasks and brainstorming
        - Technical explanations
        - Learning and education support
        
        Always be respectful, helpful, and maintain a positive tone.
        If you're unsure about something, acknowledge it honestly.
        """
    
    def get_response(self, user_message, conversation_history=None):
        """
        Get response from OpenAI API
        
        Args:
            user_message (str): The user's message
            conversation_history (list): Previous conversation context
            
        Returns:
            dict: Response containing message and metadata
        """
        try:
            # Build conversation messages
            messages = [{"role": "system", "content": self.system_prompt}]
            
            # Add conversation history if provided (limit to last 10 exchanges)
            if conversation_history:
                for exchange in conversation_history[-10:]:
                    if 'user' in exchange and 'assistant' in exchange:
                        messages.append({"role": "user", "content": exchange['user']})
                        messages.append({"role": "assistant", "content": exchange['assistant']})
            
            # Add current user message
            messages.append({"role": "user", "content": user_message})
            
            logger.info(f"Sending request to OpenAI with {len(messages)} messages")
            
            # Make API call to OpenAI
            response = self.client.chat.completions.create(
                model=self.default_model,
                messages=messages,
                max_tokens=self.max_tokens,
                temperature=self.temperature,
                presence_penalty=0.1,
                frequency_penalty=0.1
            )
            
            # Extract response content
            assistant_message = response.choices[0].message.content.strip()
            
            # Log successful response
            logger.info(f"OpenAI response received successfully. Length: {len(assistant_message)} characters")
            
            return {
                'success': True,
                'message': assistant_message,
                'model': self.default_model,
                'tokens_used': response.usage.total_tokens if hasattr(response, 'usage') else None,
                'timestamp': datetime.utcnow().isoformat()
            }
            
        except Exception as e:
            error_message = self._handle_openai_error(e)
            logger.error(f"OpenAI API error: {str(e)}")
            logger.error(f"Traceback: {traceback.format_exc()}")
            
            return {
                'success': False,
                'error': error_message,
                'error_type': type(e).__name__,
                'timestamp': datetime.utcnow().isoformat()
            }
    
    def _handle_openai_error(self, error):
        """Handle different types of OpenAI API errors"""
        error_str = str(error).lower()
        
        if "authentication" in error_str or "api key" in error_str:
            return "Authentication failed. Please check your API key configuration."
        elif "rate limit" in error_str or "quota" in error_str:
            return "Rate limit exceeded. Please try again in a moment."
        elif "timeout" in error_str:
            return "Request timed out. Please try again."
        elif "connection" in error_str:
            return "Connection error. Please check your internet connection."
        elif "invalid request" in error_str:
            return "Invalid request format. Please try rephrasing your message."
        else:
            return "An unexpected error occurred. Please try again later."

# Initialize chatbot service
chatbot_service = ChatbotService(openai_client)

@app.route('/', methods=['GET'])
def health_check():
    """Health check endpoint"""
    return jsonify({
        'status': 'healthy',
        'service': 'Chatbot Backend',
        'timestamp': datetime.utcnow().isoformat(),
        'version': '1.0.0'
    })

@app.route('/api/chat', methods=['POST'])
def chat():
    """
    Main chat endpoint for receiving user messages and returning AI responses
    
    Expected JSON payload:
    {
        "message": "User's message",
        "conversation_history": [optional array of previous exchanges]
    }
    """
    try:
        # Validate request
        if not request.is_json:
            logger.warning("Received non-JSON request")
            return jsonify({
                'success': False,
                'error': 'Request must be JSON'
            }), 400
        
        data = request.get_json()
        
        # Validate required fields
        if 'message' not in data:
            logger.warning("Missing 'message' field in request")
            return jsonify({
                'success': False,
                'error': 'Missing required field: message'
            }), 400
        
        user_message = data['message'].strip()
        
        if not user_message:
            logger.warning("Empty message received")
            return jsonify({
                'success': False,
                'error': 'Message cannot be empty'
            }), 400
        
        # Get conversation history if provided
        conversation_history = data.get('conversation_history', [])
        
        logger.info(f"Processing chat request. Message length: {len(user_message)}")
        
        # Get response from chatbot service
        response = chatbot_service.get_response(user_message, conversation_history)
        
        # Return appropriate HTTP status code
        status_code = 200 if response['success'] else 500
        
        return jsonify(response), status_code
        
    except Exception as e:
        logger.error(f"Unexpected error in chat endpoint: {str(e)}")
        logger.error(f"Traceback: {traceback.format_exc()}")
        
        return jsonify({
            'success': False,
            'error': 'Internal server error',
            'timestamp': datetime.utcnow().isoformat()
        }), 500

@app.route('/api/chat/models', methods=['GET'])
def get_available_models():
    """Get list of available OpenAI models"""
    try:
        # For this example, return commonly used models
        models = [
            {
                'id': 'gpt-3.5-turbo',
                'name': 'GPT-3.5 Turbo',
                'description': 'Fast and efficient for most conversations'
            },
            {
                'id': 'gpt-4',
                'name': 'GPT-4',
                'description': 'More capable but slower and more expensive'
            }
        ]
        
        return jsonify({
            'success': True,
            'models': models,
            'current_model': chatbot_service.default_model
        })
        
    except Exception as e:
        logger.error(f"Error getting models: {str(e)}")
        return jsonify({
            'success': False,
            'error': 'Failed to retrieve models'
        }), 500

@app.route('/api/chat/config', methods=['GET', 'POST'])
def chat_config():
    """Get or update chatbot configuration"""
    if request.method == 'GET':
        return jsonify({
            'success': True,
            'config': {
                'model': chatbot_service.default_model,
                'max_tokens': chatbot_service.max_tokens,
                'temperature': chatbot_service.temperature
            }
        })
    
    elif request.method == 'POST':
        try:
            data = request.get_json()
            
            # Update configuration
            if 'model' in data:
                chatbot_service.default_model = data['model']
            if 'max_tokens' in data:
                chatbot_service.max_tokens = int(data['max_tokens'])
            if 'temperature' in data:
                chatbot_service.temperature = float(data['temperature'])
            
            logger.info(f"Configuration updated: {data}")
            
            return jsonify({
                'success': True,
                'message': 'Configuration updated successfully',
                'config': {
                    'model': chatbot_service.default_model,
                    'max_tokens': chatbot_service.max_tokens,
                    'temperature': chatbot_service.temperature
                }
            })
            
        except Exception as e:
            logger.error(f"Error updating configuration: {str(e)}")
            return jsonify({
                'success': False,
                'error': 'Failed to update configuration'
            }), 500

@app.errorhandler(404)
def not_found(error):
    """Handle 404 errors"""
    logger.warning(f"404 error: {request.url}")
    return jsonify({
        'success': False,
        'error': 'Endpoint not found'
    }), 404

@app.errorhandler(500)
def internal_error(error):
    """Handle 500 errors"""
    logger.error(f"500 error: {str(error)}")
    return jsonify({
        'success': False,
        'error': 'Internal server error'
    }), 500

if __name__ == '__main__':
    logger.info("Starting Chatbot Backend Server...")
    logger.info(f"OpenAI API Key configured: {'Yes' if OPENAI_API_KEY else 'No'}")
    
    # Run the Flask app
    app.run(
        host='0.0.0.0',
        port=int(os.getenv('PORT', 5000)),
        debug=os.getenv('FLASK_ENV') == 'development'
    )
