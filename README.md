# 🌌 Galaxy Mentor - AI Personal Coach

A beautiful, modern Galaxy-themed chatbot website where users can sign up, sign in, and interact with an AI personal mentor. The chatbot provides guidance on self-development, productivity, programming, and tech tips using OpenAI's GPT models.

## ✨ Features

- **🎨 Galaxy-themed UI**: Beautiful dark mode design with animated stars and cosmic gradients
- **🔐 User Authentication**: Secure sign up and sign in functionality
- **🤖 AI Chatbot**: Personal mentor powered by OpenAI GPT-3.5-turbo
- **💬 Real-time Chat**: Clean, animated chat interface with typing indicators
- **📱 Responsive Design**: Works perfectly on desktop, tablet, and mobile
- **💾 Chat History**: Persistent conversation history for each user
- **🚀 Modern Tech Stack**: Flask, SQLite, HTML5, CSS3, JavaScript

## 🛠️ Tech Stack

- **Backend**: Python Flask
- **Database**: SQLite with SQLAlchemy
- **Frontend**: HTML5, CSS3, JavaScript
- **AI**: OpenAI GPT-3.5-turbo API
- **Styling**: Custom CSS with CSS Grid and Flexbox
- **Icons**: Font Awesome
- **Fonts**: Inter & Space Grotesk

## 🚀 Quick Start

### Prerequisites

- Python 3.8 or higher
- OpenAI API key ([Get one here](https://platform.openai.com/api-keys))

### Installation

1. **Clone or download the project**
   ```bash
   cd galaxy-mentor
   ```

2. **Install dependencies**
   ```bash
   pip install -r requirements.txt
   ```

3. **Configure environment variables**
   - Copy `.env.example` to `.env`
   - Add your OpenAI API key to `.env`:
   ```
   OPENAI_API_KEY=your_actual_api_key_here
   FLASK_SECRET_KEY=your_secret_key_here
   ```

4. **Run the application**
   ```bash
   python app.py
   ```

5. **Open your browser**
   - Navigate to `http://localhost:5000`
   - Create an account and start chatting with Galaxy Mentor!

## 📁 Project Structure

```
galaxy-mentor/
├── app.py                 # Main Flask application
├── models.py             # Database models
├── auth.py               # Authentication utilities
├── chatbot.py            # OpenAI chatbot logic
├── requirements.txt      # Python dependencies
├── .env                  # Environment variables
├── .env.example         # Environment template
├── .gitignore           # Git ignore rules
├── templates/           # HTML templates
│   ├── base.html        # Base template
│   ├── index.html       # Landing page
│   ├── login.html       # Login page
│   ├── register.html    # Registration page
│   └── chat.html        # Chat interface
└── static/              # Static assets
    ├── css/
    │   └── style.css    # Main stylesheet
    └── js/
        ├── main.js      # Main JavaScript
        └── auth.js      # Authentication JS
```

## 🎯 Galaxy Mentor Capabilities

The AI mentor specializes in:

- **🧠 Self-Development**: Personal growth, goal setting, habit building
- **⚡ Productivity**: Time management, focus techniques, workflow optimization
- **💻 Programming**: Learning paths, best practices, career advice
- **🔧 Tech Tips**: Tools, trends, and practical technology guidance

## 🎨 Design Features

- **Animated Background**: Twinkling stars with multiple layers
- **Gradient Themes**: Purple, blue, and pink cosmic gradients
- **Smooth Animations**: Hover effects, transitions, and micro-interactions
- **Typography**: Modern font pairing with excellent readability
- **Responsive Layout**: Mobile-first design approach

## 🔧 Configuration

### Environment Variables

- `OPENAI_API_KEY`: Your OpenAI API key
- `FLASK_SECRET_KEY`: Secret key for Flask sessions
- `FLASK_ENV`: Environment (development/production)

### Customization

- **Colors**: Modify CSS variables in `static/css/style.css`
- **Mentor Personality**: Edit the system prompt in `chatbot.py`
- **UI Components**: Update templates in the `templates/` folder

## 🚀 Deployment

### Local Development
```bash
python app.py
```

### Production Deployment
1. Set `FLASK_ENV=production` in `.env`
2. Use a production WSGI server like Gunicorn:
   ```bash
   pip install gunicorn
   gunicorn -w 4 -b 0.0.0.0:5000 app:app
   ```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## 📝 License

This project is open source and available under the MIT License.

## 🆘 Support

If you encounter any issues:

1. Check that your OpenAI API key is valid and has credits
2. Ensure all dependencies are installed correctly
3. Verify your Python version is 3.8+
4. Check the console for error messages

## 🌟 Features Coming Soon

- **🎨 Theme Customization**: Multiple color themes
- **🔊 Voice Chat**: Speech-to-text and text-to-speech
- **📊 Analytics**: Personal growth tracking
- **🤝 Social Features**: Share insights and progress
- **📱 Mobile App**: Native mobile applications

---

**Built with ❤️ and cosmic inspiration** ✨

Enjoy your journey with Galaxy Mentor! 🚀
