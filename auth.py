"""
Authentication utilities for the Galaxy Chatbot application.
"""

from functools import wraps
from flask import session, redirect, url_for, flash, request
from models import User, db

def login_required(f):
    """
    Decorator to require login for protected routes.
    """
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if 'user_id' not in session:
            flash('Please log in to access the Galaxy Mentor.', 'warning')
            return redirect(url_for('login'))
        return f(*args, **kwargs)
    return decorated_function

def register_user(username, email, password):
    """
    Register a new user with validation.
    
    Args:
        username (str): Desired username
        email (str): User's email address
        password (str): User's password
        
    Returns:
        tuple: (success: bool, message: str, user: User or None)
    """
    # Validate input
    if not username or not email or not password:
        return False, "All fields are required.", None
    
    if len(username) < 3:
        return False, "Username must be at least 3 characters long.", None
    
    if len(password) < 6:
        return False, "Password must be at least 6 characters long.", None
    
    # Check if user already exists
    if User.query.filter_by(username=username).first():
        return False, "Username already exists. Choose a different one.", None
    
    if User.query.filter_by(email=email).first():
        return False, "Email already registered. Try logging in instead.", None
    
    try:
        # Create new user
        user = User(username=username, email=email)
        user.set_password(password)
        
        db.session.add(user)
        db.session.commit()
        
        return True, "Account created successfully! Welcome to the Galaxy!", user
        
    except Exception as e:
        db.session.rollback()
        return False, "An error occurred while creating your account. Please try again.", None

def authenticate_user(username, password):
    """
    Authenticate a user login attempt.
    
    Args:
        username (str): Username or email
        password (str): User's password
        
    Returns:
        tuple: (success: bool, message: str, user: User or None)
    """
    if not username or not password:
        return False, "Please enter both username and password.", None
    
    # Try to find user by username or email
    user = User.query.filter(
        (User.username == username) | (User.email == username)
    ).first()
    
    if not user:
        return False, "Invalid username or password.", None
    
    if not user.check_password(password):
        return False, "Invalid username or password.", None
    
    return True, "Welcome back to the Galaxy!", user

def login_user(user):
    """
    Log in a user by setting session variables.
    
    Args:
        user (User): The user to log in
    """
    session['user_id'] = user.id
    session['username'] = user.username

def logout_user():
    """
    Log out the current user by clearing session.
    """
    session.clear()

def get_current_user():
    """
    Get the currently logged-in user.
    
    Returns:
        User or None: The current user if logged in, None otherwise
    """
    if 'user_id' in session:
        return User.query.get(session['user_id'])
    return None
