/**
 * Galaxy Mentor - Authentication JavaScript
 * Handles login and registration form functionality
 */

document.addEventListener('DOMContentLoaded', function() {
    initializeAuthForms();
});

/**
 * Initialize authentication forms
 */
function initializeAuthForms() {
    const loginForm = document.getElementById('loginForm');
    const registerForm = document.getElementById('registerForm');
    
    if (loginForm) {
        initializeLoginForm(loginForm);
    }
    
    if (registerForm) {
        initializeRegisterForm(registerForm);
    }
    
    // Initialize password toggles
    initializePasswordToggles();
    
    console.log('🔐 Authentication forms initialized');
}

/**
 * Initialize login form
 */
function initializeLoginForm(form) {
    const usernameInput = form.querySelector('#username');
    const passwordInput = form.querySelector('#password');
    const submitButton = form.querySelector('button[type="submit"]');
    
    // Real-time validation
    usernameInput.addEventListener('input', function() {
        validateLoginField(this, 'username');
        updateSubmitButton(form);
    });
    
    passwordInput.addEventListener('input', function() {
        validateLoginField(this, 'password');
        updateSubmitButton(form);
    });
    
    // Form submission
    form.addEventListener('submit', function(e) {
        if (!validateLoginForm(form)) {
            e.preventDefault();
            return false;
        }
        
        // Show loading state
        submitButton.classList.add('loading');
        submitButton.disabled = true;
    });
}

/**
 * Initialize registration form
 */
function initializeRegisterForm(form) {
    const usernameInput = form.querySelector('#username');
    const emailInput = form.querySelector('#email');
    const passwordInput = form.querySelector('#password');
    const confirmPasswordInput = form.querySelector('#confirmPassword');
    const submitButton = form.querySelector('button[type="submit"]');
    
    // Real-time validation
    usernameInput.addEventListener('input', function() {
        validateRegisterField(this, 'username');
        updateSubmitButton(form);
    });
    
    emailInput.addEventListener('input', function() {
        validateRegisterField(this, 'email');
        updateSubmitButton(form);
    });
    
    passwordInput.addEventListener('input', function() {
        validateRegisterField(this, 'password');
        updateSubmitButton(form);
        
        // Also validate confirm password if it has a value
        if (confirmPasswordInput.value) {
            validateRegisterField(confirmPasswordInput, 'confirmPassword');
        }
    });
    
    confirmPasswordInput.addEventListener('input', function() {
        validateRegisterField(this, 'confirmPassword');
        updateSubmitButton(form);
    });
    
    // Form submission
    form.addEventListener('submit', function(e) {
        if (!validateRegisterForm(form)) {
            e.preventDefault();
            return false;
        }
        
        // Show loading state
        submitButton.classList.add('loading');
        submitButton.disabled = true;
    });
}

/**
 * Initialize password toggle functionality
 */
function initializePasswordToggles() {
    const passwordToggles = document.querySelectorAll('.password-toggle');
    
    passwordToggles.forEach(toggle => {
        toggle.addEventListener('click', function() {
            const targetId = this.getAttribute('onclick').match(/'([^']+)'/)[1];
            togglePassword(targetId);
        });
    });
}

/**
 * Toggle password visibility
 */
function togglePassword(inputId) {
    const input = document.getElementById(inputId);
    const toggle = input.parentElement.querySelector('.password-toggle i');
    
    if (input.type === 'password') {
        input.type = 'text';
        toggle.className = 'fas fa-eye-slash';
    } else {
        input.type = 'password';
        toggle.className = 'fas fa-eye';
    }
}

/**
 * Validate login form field
 */
function validateLoginField(field, type) {
    const value = field.value.trim();
    let isValid = true;
    let message = '';
    
    switch (type) {
        case 'username':
            if (!value) {
                isValid = false;
                message = 'Username or email is required';
            }
            break;
            
        case 'password':
            if (!value) {
                isValid = false;
                message = 'Password is required';
            }
            break;
    }
    
    updateFieldValidation(field, isValid, message);
    return isValid;
}

/**
 * Validate registration form field
 */
function validateRegisterField(field, type) {
    const value = field.value.trim();
    let isValid = true;
    let message = '';
    
    switch (type) {
        case 'username':
            if (!value) {
                isValid = false;
                message = 'Username is required';
            } else if (value.length < 3) {
                isValid = false;
                message = 'Username must be at least 3 characters';
            } else if (!/^[a-zA-Z0-9_]+$/.test(value)) {
                isValid = false;
                message = 'Username can only contain letters, numbers, and underscores';
            }
            break;
            
        case 'email':
            if (!value) {
                isValid = false;
                message = 'Email is required';
            } else if (!window.GalaxyMentor.isValidEmail(value)) {
                isValid = false;
                message = 'Please enter a valid email address';
            }
            break;
            
        case 'password':
            if (!value) {
                isValid = false;
                message = 'Password is required';
            } else {
                const validation = window.GalaxyMentor.validatePasswordStrength(value);
                if (!validation.isValid) {
                    isValid = false;
                    message = 'Password must be at least 6 characters with letters and numbers';
                }
            }
            break;
            
        case 'confirmPassword':
            const passwordField = document.getElementById('password');
            if (!value) {
                isValid = false;
                message = 'Please confirm your password';
            } else if (value !== passwordField.value) {
                isValid = false;
                message = 'Passwords do not match';
            }
            break;
    }
    
    updateFieldValidation(field, isValid, message);
    return isValid;
}

/**
 * Update field validation UI
 */
function updateFieldValidation(field, isValid, message) {
    const formGroup = field.closest('.form-group');
    const existingError = formGroup.querySelector('.field-error');
    
    // Remove existing error
    if (existingError) {
        existingError.remove();
    }
    
    // Update field styling
    if (field.value.trim()) {
        if (isValid) {
            field.style.borderColor = 'var(--success)';
        } else {
            field.style.borderColor = 'var(--error)';
            
            // Add error message
            const errorElement = document.createElement('div');
            errorElement.className = 'field-error';
            errorElement.style.cssText = `
                color: var(--error);
                font-size: 0.875rem;
                margin-top: var(--spacing-xs);
            `;
            errorElement.textContent = message;
            formGroup.appendChild(errorElement);
        }
    } else {
        field.style.borderColor = '';
    }
}

/**
 * Validate entire login form
 */
function validateLoginForm(form) {
    const username = form.querySelector('#username');
    const password = form.querySelector('#password');
    
    const isUsernameValid = validateLoginField(username, 'username');
    const isPasswordValid = validateLoginField(password, 'password');
    
    return isUsernameValid && isPasswordValid;
}

/**
 * Validate entire registration form
 */
function validateRegisterForm(form) {
    const username = form.querySelector('#username');
    const email = form.querySelector('#email');
    const password = form.querySelector('#password');
    const confirmPassword = form.querySelector('#confirmPassword');
    
    const isUsernameValid = validateRegisterField(username, 'username');
    const isEmailValid = validateRegisterField(email, 'email');
    const isPasswordValid = validateRegisterField(password, 'password');
    const isConfirmPasswordValid = validateRegisterField(confirmPassword, 'confirmPassword');
    
    return isUsernameValid && isEmailValid && isPasswordValid && isConfirmPasswordValid;
}

/**
 * Update submit button state
 */
function updateSubmitButton(form) {
    const submitButton = form.querySelector('button[type="submit"]');
    const isLoginForm = form.id === 'loginForm';
    
    let isFormValid;
    if (isLoginForm) {
        isFormValid = validateLoginForm(form);
    } else {
        isFormValid = validateRegisterForm(form);
    }
    
    // Check if all required fields have values
    const requiredFields = form.querySelectorAll('input[required]');
    const allFieldsFilled = Array.from(requiredFields).every(field => field.value.trim());
    
    if (isFormValid && allFieldsFilled) {
        submitButton.disabled = false;
        submitButton.style.opacity = '1';
    } else {
        submitButton.disabled = true;
        submitButton.style.opacity = '0.6';
    }
}

/**
 * Show password strength indicator
 */
function showPasswordStrength(password) {
    const validation = window.GalaxyMentor.validatePasswordStrength(password);
    const strengthIndicator = document.querySelector('.password-strength');
    
    if (!strengthIndicator) {
        return;
    }
    
    const strengthLevels = ['Very Weak', 'Weak', 'Fair', 'Good', 'Strong'];
    const strengthColors = ['#e17055', '#fdcb6e', '#f39c12', '#00b894', '#00b894'];
    
    strengthIndicator.textContent = strengthLevels[validation.score - 1] || 'Very Weak';
    strengthIndicator.style.color = strengthColors[validation.score - 1] || strengthColors[0];
}

/**
 * Handle social login (placeholder for future implementation)
 */
function handleSocialLogin(provider) {
    window.GalaxyMentor.showToast(`${provider} login coming soon!`, 'info');
}

// Make functions available globally
window.togglePassword = togglePassword;
window.handleSocialLogin = handleSocialLogin;
