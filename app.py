"""
Galaxy Chatbot - A personal mentor assistant with galaxy-themed UI.
Main Flask application with authentication and chatbot functionality.
"""

import os
from flask import Flask, render_template, request, jsonify, redirect, url_for, flash, session
from dotenv import load_dotenv
from models import init_db, User, ChatMessage, db
from auth import login_required, register_user, authenticate_user, login_user, logout_user, get_current_user
from chatbot import GalaxyMentor

# Load environment variables
load_dotenv()

# Initialize Flask app
app = Flask(__name__)
app.config['SECRET_KEY'] = os.getenv('FLASK_SECRET_KEY', 'galaxy-secret-key-change-in-production')
app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///galaxy_chatbot.db'
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False

# Initialize database
init_db(app)

# Initialize chatbot
mentor = GalaxyMentor()

@app.route('/')
def index():
    """Landing page - redirect to chat if logged in, otherwise show welcome."""
    if 'user_id' in session:
        return redirect(url_for('chat'))
    return render_template('index.html')

@app.route('/register', methods=['GET', 'POST'])
def register():
    """User registration page and handler."""
    if request.method == 'POST':
        username = request.form.get('username', '').strip()
        email = request.form.get('email', '').strip()
        password = request.form.get('password', '')
        
        success, message, user = register_user(username, email, password)
        
        if success:
            login_user(user)
            flash(message, 'success')
            return redirect(url_for('chat'))
        else:
            flash(message, 'error')
    
    return render_template('register.html')

@app.route('/login', methods=['GET', 'POST'])
def login():
    """User login page and handler."""
    if request.method == 'POST':
        username = request.form.get('username', '').strip()
        password = request.form.get('password', '')
        
        success, message, user = authenticate_user(username, password)
        
        if success:
            login_user(user)
            flash(message, 'success')
            return redirect(url_for('chat'))
        else:
            flash(message, 'error')
    
    return render_template('login.html')

@app.route('/logout')
@login_required
def logout():
    """Log out the current user."""
    logout_user()
    flash('You have been logged out. See you in the galaxy!', 'info')
    return redirect(url_for('index'))

@app.route('/chat')
@login_required
def chat():
    """Main chat interface for logged-in users."""
    user = get_current_user()
    return render_template('chat.html', user=user)

@app.route('/api/chat', methods=['POST'])
@login_required
def api_chat():
    """API endpoint for chat messages."""
    try:
        data = request.get_json()
        user_message = data.get('message', '').strip()
        
        if not user_message:
            return jsonify({'error': 'Message cannot be empty'}), 400
        
        user = get_current_user()
        
        # Get conversation history for context
        recent_messages = ChatMessage.query.filter_by(user_id=user.id)\
                                         .order_by(ChatMessage.timestamp.desc())\
                                         .limit(10).all()
        
        # Format conversation history for the chatbot
        conversation_history = []
        for msg in reversed(recent_messages):
            conversation_history.append({
                'user': msg.message,
                'assistant': msg.response
            })
        
        # Get response from Galaxy Mentor
        bot_response = mentor.get_response(user_message, conversation_history)
        
        # Save the conversation to database
        chat_message = ChatMessage(
            user_id=user.id,
            message=user_message,
            response=bot_response
        )
        db.session.add(chat_message)
        db.session.commit()
        
        return jsonify({
            'response': bot_response,
            'timestamp': chat_message.timestamp.isoformat()
        })
        
    except Exception as e:
        return jsonify({'error': 'An error occurred processing your message'}), 500

@app.route('/api/welcome')
@login_required
def api_welcome():
    """API endpoint to get welcome message for new users."""
    user = get_current_user()
    welcome_message = mentor.get_welcome_message(user.username)
    return jsonify({'message': welcome_message})

@app.route('/api/history')
@login_required
def api_history():
    """API endpoint to get chat history."""
    user = get_current_user()
    messages = ChatMessage.query.filter_by(user_id=user.id)\
                               .order_by(ChatMessage.timestamp.asc())\
                               .limit(50).all()
    
    history = []
    for msg in messages:
        history.append({
            'user_message': msg.message,
            'bot_response': msg.response,
            'timestamp': msg.timestamp.isoformat()
        })
    
    return jsonify({'history': history})

if __name__ == '__main__':
    app.run(debug=True, host='0.0.0.0', port=5000)
